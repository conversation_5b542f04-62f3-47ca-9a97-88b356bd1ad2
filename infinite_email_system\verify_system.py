#!/usr/bin/env python3
"""
系统验证脚本 - 验证无限邮箱系统的完整性和功能
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv()

async def verify_configuration():
    """验证配置"""
    print("🔧 验证系统配置...")
    
    # 检查环境变量
    required_vars = {
        'CLOUDFLARE_API_TOKEN': '您的 Cloudflare API Token',
        'CLOUDFLARE_ZONE_ID': '733e04386ef1d5d5ffee19a6f74e8b07',
        'DOMAIN_NAME': 'cfish22.dpdns.org',
        'DEFAULT_FORWARD_EMAIL': '<EMAIL>'
    }
    
    all_configured = True
    for var, expected in required_vars.items():
        value = os.getenv(var)
        if not value:
            print(f"❌ 缺少环境变量: {var}")
            all_configured = False
        elif var == 'CLOUDFLARE_API_TOKEN' and (value.startswith('请替换') or len(value) != 40):
            print(f"❌ {var} 未正确配置")
            all_configured = False
        else:
            print(f"✅ {var}: {value}")
    
    return all_configured

async def verify_cloudflare_api():
    """验证 Cloudflare API"""
    print("\n🌐 验证 Cloudflare API...")
    
    try:
        from core.cloudflare_api import CloudflareEmailAPI
        
        api_token = os.getenv('CLOUDFLARE_API_TOKEN')
        zone_id = os.getenv('CLOUDFLARE_ZONE_ID')
        
        if not api_token or api_token.startswith('请替换'):
            print("❌ API Token 未配置")
            return False
        
        async with CloudflareEmailAPI(api_token) as api:
            # 测试连接
            if not await api.test_connection():
                print("❌ API 连接失败")
                return False
            
            print("✅ API 连接成功")
            
            # 获取邮件路由状态
            stats = await api.get_email_routing_stats(zone_id)
            
            if stats.get('enabled'):
                print("✅ 邮件路由已启用")
                print(f"📋 转发规则: {stats.get('rules_count', 0)} 条")
                print(f"📮 目标地址: {stats.get('addresses_count', 0)} 个")
                
                # 检查目标地址验证状态
                addresses = stats.get('addresses', [])
                verified_count = sum(1 for addr in addresses if addr.get('verified', False))
                print(f"✅ 已验证地址: {verified_count}/{len(addresses)}")
                
                if verified_count == 0 and addresses:
                    print("⚠️  请检查邮箱并验证目标地址")
                
                return True
            else:
                print("❌ 邮件路由未启用")
                return False
                
    except Exception as e:
        print(f"❌ Cloudflare API 验证失败: {e}")
        return False

async def verify_email_generation():
    """验证邮箱生成功能"""
    print("\n📧 验证邮箱生成功能...")
    
    try:
        from core.email_generator import EmailGenerator
        
        generator = EmailGenerator()
        domain = os.getenv('DOMAIN_NAME', 'cfish22.dpdns.org')
        
        # 生成测试邮箱
        test_emails = generator.generate_batch_emails(domain, 3, pattern='mixed')
        
        if test_emails:
            print("✅ 邮箱生成功能正常")
            print("📧 示例邮箱:")
            for i, email in enumerate(test_emails, 1):
                print(f"  {i}. {email}")
            return True
        else:
            print("❌ 邮箱生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 邮箱生成验证失败: {e}")
        return False

async def verify_database():
    """验证数据库功能"""
    print("\n🗄️  验证数据库功能...")
    
    try:
        from config.database import DatabaseManager
        
        db_path = 'data/emails.db'
        db = DatabaseManager(db_path)
        await db.initialize()
        
        # 测试数据库操作
        stats = await db.get_statistics()
        print("✅ 数据库连接正常")
        print(f"📊 统计信息: {stats}")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False

async def verify_complete_workflow():
    """验证完整工作流程"""
    print("\n🔄 验证完整工作流程...")
    
    try:
        from core.email_generator import EmailGenerator
        from config.database import DatabaseManager
        
        # 1. 生成邮箱
        generator = EmailGenerator()
        domain = os.getenv('DOMAIN_NAME')
        prefix = generator.generate_random_prefix(length=8, pattern='mixed')
        email = f"{prefix}@{domain}"
        
        print(f"📧 生成测试邮箱: {email}")
        
        # 2. 保存到数据库
        db = DatabaseManager('data/emails.db')
        await db.initialize()
        
        email_id = await db.save_email(email, domain, prefix)
        print(f"💾 邮箱已保存到数据库 (ID: {email_id})")
        
        # 3. 验证保存
        saved_email = await db.get_email_by_address(email)
        if saved_email:
            print("✅ 数据库保存验证成功")
        else:
            print("❌ 数据库保存验证失败")
            return False
        
        await db.close()
        
        print("✅ 完整工作流程验证成功")
        return True
        
    except Exception as e:
        print(f"❌ 完整工作流程验证失败: {e}")
        return False

def show_system_status():
    """显示系统状态"""
    domain = os.getenv('DOMAIN_NAME', 'cfish22.dpdns.org')
    forward_email = os.getenv('DEFAULT_FORWARD_EMAIL', '<EMAIL>')
    
    print("\n" + "=" * 60)
    print("📋 系统状态总览")
    print("=" * 60)
    print(f"🌐 主域名: {domain}")
    print(f"📮 转发邮箱: {forward_email}")
    print(f"🔧 配置文件: .env")
    print(f"🗄️  数据库: data/emails.db")
    print("=" * 60)

async def main():
    """主验证函数"""
    print("🔍 无限邮箱系统 - 系统验证")
    print("=" * 60)
    
    verification_results = []
    
    # 1. 验证配置
    config_ok = await verify_configuration()
    verification_results.append(("配置验证", config_ok))
    
    # 2. 验证 Cloudflare API
    if config_ok:
        api_ok = await verify_cloudflare_api()
        verification_results.append(("Cloudflare API", api_ok))
    else:
        verification_results.append(("Cloudflare API", False))
        api_ok = False
    
    # 3. 验证邮箱生成
    email_gen_ok = await verify_email_generation()
    verification_results.append(("邮箱生成", email_gen_ok))
    
    # 4. 验证数据库
    db_ok = await verify_database()
    verification_results.append(("数据库", db_ok))
    
    # 5. 验证完整工作流程
    if email_gen_ok and db_ok:
        workflow_ok = await verify_complete_workflow()
        verification_results.append(("完整工作流程", workflow_ok))
    else:
        verification_results.append(("完整工作流程", False))
    
    # 显示验证结果
    print("\n" + "=" * 60)
    print("📊 验证结果")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in verification_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("🎉 所有验证通过！系统已就绪。")
        show_system_status()
        print("\n💡 现在可以运行 python start.py 启动系统")
    else:
        print("⚠️  部分验证失败，请检查配置和网络连接。")
        print("\n🔧 建议:")
        print("1. 检查 .env 文件中的配置")
        print("2. 确保 Cloudflare API Token 正确")
        print("3. 验证目标邮箱地址")
        print("4. 检查网络连接")
    
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
