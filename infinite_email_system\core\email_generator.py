"""
邮箱生成核心类
"""

import random
import string
import time
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime

from utils.helpers import generate_random_string, generate_unique_id
from utils.validators import EmailValidator, DomainValidator
from utils.exceptions import EmailGenerationError
from utils.logger import LoggerMixin
from config.constants import DEFAULT_EMAIL_PREFIX_LENGTH, MAX_BATCH_SIZE


class EmailGenerator(LoggerMixin):
    """邮箱生成核心类"""
    
    def __init__(self):
        self.patterns = ['random', 'meaningful', 'sequential', 'uuid', 'timestamp']
        self.meaningful_words = [
            'user', 'test', 'demo', 'temp', 'mail', 'info', 'admin', 'support',
            'contact', 'hello', 'welcome', 'service', 'help', 'team', 'dev',
            'api', 'web', 'app', 'system', 'auto', 'bot', 'tool', 'util'
        ]
        self.sequential_counter = 0
    
    def generate_random_prefix(self, 
                              length: int = DEFAULT_EMAIL_PREFIX_LENGTH,
                              pattern: str = 'mixed',
                              exclude_ambiguous: bool = True) -> str:
        """
        生成随机邮箱前缀
        
        Args:
            length: 前缀长度
            pattern: 生成模式 ('letters', 'numbers', 'mixed', 'meaningful', 'uuid', 'timestamp')
            exclude_ambiguous: 是否排除易混淆字符
        
        Returns:
            随机前缀
        """
        try:
            if pattern == 'letters':
                chars = string.ascii_lowercase
            elif pattern == 'numbers':
                chars = string.digits
            elif pattern == 'mixed':
                chars = string.ascii_lowercase + string.digits
            elif pattern == 'meaningful':
                return self._generate_meaningful_prefix(length)
            elif pattern == 'uuid':
                return str(uuid.uuid4()).replace('-', '')[:length]
            elif pattern == 'timestamp':
                return self._generate_timestamp_prefix(length)
            elif pattern == 'sequential':
                return self._generate_sequential_prefix(length)
            else:
                chars = string.ascii_lowercase + string.digits
            
            if exclude_ambiguous:
                # 排除易混淆的字符
                ambiguous = "0O1lI"
                chars = ''.join(c for c in chars if c not in ambiguous)
            
            # 确保首字符是字母
            if pattern in ['mixed', 'letters']:
                first_char = random.choice(string.ascii_lowercase)
                if exclude_ambiguous:
                    first_char = random.choice([c for c in string.ascii_lowercase if c not in "lI"])
                remaining_chars = ''.join(random.choices(chars, k=length-1))
                return first_char + remaining_chars
            else:
                return ''.join(random.choices(chars, k=length))
                
        except Exception as e:
            raise EmailGenerationError(f"Failed to generate prefix: {e}")
    
    def _generate_meaningful_prefix(self, length: int) -> str:
        """生成有意义的前缀"""
        base_word = random.choice(self.meaningful_words)
        
        if len(base_word) >= length:
            return base_word[:length]
        
        # 添加数字或随机字符
        remaining_length = length - len(base_word)
        if remaining_length <= 3:
            # 添加数字
            suffix = ''.join(random.choices(string.digits, k=remaining_length))
        else:
            # 添加随机字符
            suffix = generate_random_string(remaining_length, include_symbols=False)
        
        return base_word + suffix
    
    def _generate_timestamp_prefix(self, length: int) -> str:
        """生成基于时间戳的前缀"""
        timestamp = str(int(time.time()))
        
        if len(timestamp) >= length:
            return timestamp[-length:]
        
        # 添加随机前缀
        prefix_length = length - len(timestamp)
        prefix = generate_random_string(prefix_length, include_symbols=False)
        return prefix + timestamp
    
    def _generate_sequential_prefix(self, length: int) -> str:
        """生成序列号前缀"""
        self.sequential_counter += 1
        counter_str = str(self.sequential_counter)
        
        if len(counter_str) >= length:
            return counter_str[-length:]
        
        # 添加前缀
        prefix_length = length - len(counter_str)
        prefix = 'user' if prefix_length >= 4 else 'u' * prefix_length
        if len(prefix) > prefix_length:
            prefix = prefix[:prefix_length]
        
        return prefix + counter_str
    
    def generate_batch_emails(self, 
                             domain: str, 
                             count: int,
                             pattern: str = 'mixed',
                             prefix_length: int = DEFAULT_EMAIL_PREFIX_LENGTH,
                             ensure_unique: bool = True) -> List[str]:
        """
        批量生成邮箱地址
        
        Args:
            domain: 域名
            count: 生成数量
            pattern: 生成模式
            prefix_length: 前缀长度
            ensure_unique: 是否确保唯一性
        
        Returns:
            邮箱地址列表
        """
        try:
            if count > MAX_BATCH_SIZE:
                raise EmailGenerationError(f"Batch size exceeds maximum: {MAX_BATCH_SIZE}")
            
            if not DomainValidator.validate_domain(domain):
                raise EmailGenerationError(f"Invalid domain: {domain}")
            
            emails = []
            generated_prefixes = set()
            max_attempts = count * 10  # 防止无限循环
            attempts = 0
            
            while len(emails) < count and attempts < max_attempts:
                attempts += 1
                
                prefix = self.generate_random_prefix(prefix_length, pattern)
                
                if ensure_unique and prefix in generated_prefixes:
                    continue
                
                email = f"{prefix}@{domain}"
                
                if EmailValidator.validate_email(email):
                    emails.append(email)
                    if ensure_unique:
                        generated_prefixes.add(prefix)
            
            if len(emails) < count:
                self.logger.warning(f"Only generated {len(emails)} emails out of {count} requested")
            
            self.logger.info(f"Generated {len(emails)} emails for domain {domain}")
            return emails
            
        except Exception as e:
            raise EmailGenerationError(f"Failed to generate batch emails: {e}")
    
    def generate_email_variations(self, base_email: str, count: int = 5) -> List[str]:
        """
        生成邮箱变体
        
        Args:
            base_email: 基础邮箱
            count: 变体数量
        
        Returns:
            邮箱变体列表
        """
        try:
            if not EmailValidator.validate_email(base_email):
                raise EmailGenerationError(f"Invalid base email: {base_email}")
            
            prefix, domain = base_email.split('@', 1)
            variations = []
            
            for i in range(count):
                # 不同的变体策略
                if i % 4 == 0:
                    # 添加数字后缀
                    new_prefix = f"{prefix}{random.randint(100, 999)}"
                elif i % 4 == 1:
                    # 添加随机字符
                    new_prefix = f"{prefix}{generate_random_string(2, include_symbols=False)}"
                elif i % 4 == 2:
                    # 在中间插入数字
                    mid_point = len(prefix) // 2
                    new_prefix = prefix[:mid_point] + str(random.randint(10, 99)) + prefix[mid_point:]
                else:
                    # 添加下划线和数字
                    new_prefix = f"{prefix}_{random.randint(10, 99)}"
                
                new_email = f"{new_prefix}@{domain}"
                if EmailValidator.validate_email(new_email):
                    variations.append(new_email)
            
            return variations
            
        except Exception as e:
            raise EmailGenerationError(f"Failed to generate email variations: {e}")
    
    def validate_email_format(self, email: str) -> bool:
        """验证邮箱格式"""
        return EmailValidator.validate_email(email)
    
    def get_email_statistics(self, emails: List[str]) -> Dict[str, Any]:
        """
        获取邮箱统计信息
        
        Args:
            emails: 邮箱列表
        
        Returns:
            统计信息
        """
        try:
            if not emails:
                return {
                    'total_count': 0,
                    'valid_count': 0,
                    'invalid_count': 0,
                    'domains': {},
                    'prefix_lengths': {},
                    'patterns': {}
                }
            
            valid_emails = []
            invalid_emails = []
            domains = {}
            prefix_lengths = {}
            patterns = {'letters': 0, 'numbers': 0, 'mixed': 0, 'special': 0}
            
            for email in emails:
                if EmailValidator.validate_email(email):
                    valid_emails.append(email)
                    
                    # 分析域名
                    if '@' in email:
                        prefix, domain = email.split('@', 1)
                        domains[domain] = domains.get(domain, 0) + 1
                        
                        # 分析前缀长度
                        length = len(prefix)
                        prefix_lengths[length] = prefix_lengths.get(length, 0) + 1
                        
                        # 分析前缀模式
                        if prefix.isalpha():
                            patterns['letters'] += 1
                        elif prefix.isdigit():
                            patterns['numbers'] += 1
                        elif prefix.isalnum():
                            patterns['mixed'] += 1
                        else:
                            patterns['special'] += 1
                else:
                    invalid_emails.append(email)
            
            return {
                'total_count': len(emails),
                'valid_count': len(valid_emails),
                'invalid_count': len(invalid_emails),
                'domains': domains,
                'prefix_lengths': prefix_lengths,
                'patterns': patterns,
                'average_prefix_length': sum(k * v for k, v in prefix_lengths.items()) / len(valid_emails) if valid_emails else 0
            }
            
        except Exception as e:
            raise EmailGenerationError(f"Failed to get email statistics: {e}")
    
    def filter_emails_by_pattern(self, emails: List[str], pattern: str) -> List[str]:
        """根据模式过滤邮箱"""
        try:
            filtered = []
            
            for email in emails:
                if '@' not in email:
                    continue
                
                prefix = email.split('@')[0]
                
                if pattern == 'letters' and prefix.isalpha():
                    filtered.append(email)
                elif pattern == 'numbers' and prefix.isdigit():
                    filtered.append(email)
                elif pattern == 'mixed' and prefix.isalnum():
                    filtered.append(email)
                elif pattern == 'special' and not prefix.isalnum():
                    filtered.append(email)
            
            return filtered
            
        except Exception as e:
            raise EmailGenerationError(f"Failed to filter emails: {e}")
    
    def generate_custom_email(self, 
                             domain: str,
                             prefix_template: str = None,
                             variables: Dict[str, Any] = None) -> str:
        """
        生成自定义邮箱
        
        Args:
            domain: 域名
            prefix_template: 前缀模板 (如: "user_{id}_{timestamp}")
            variables: 模板变量
        
        Returns:
            自定义邮箱
        """
        try:
            if not DomainValidator.validate_domain(domain):
                raise EmailGenerationError(f"Invalid domain: {domain}")
            
            if prefix_template is None:
                prefix = self.generate_random_prefix()
            else:
                # 替换模板变量
                prefix = prefix_template
                if variables:
                    for key, value in variables.items():
                        prefix = prefix.replace(f"{{{key}}}", str(value))
                
                # 替换内置变量
                prefix = prefix.replace("{id}", generate_unique_id()[:8])
                prefix = prefix.replace("{timestamp}", str(int(time.time())))
                prefix = prefix.replace("{random}", generate_random_string(4, include_symbols=False))
                prefix = prefix.replace("{uuid}", str(uuid.uuid4()).replace('-', '')[:8])
            
            email = f"{prefix}@{domain}"
            
            if not EmailValidator.validate_email(email):
                raise EmailGenerationError(f"Generated invalid email: {email}")
            
            return email
            
        except Exception as e:
            raise EmailGenerationError(f"Failed to generate custom email: {e}")
