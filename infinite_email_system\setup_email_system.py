#!/usr/bin/env python3
"""
完整的邮件系统设置脚本
为 cfish22.dpdns.org 配置完整的邮件转发系统
"""

import asyncio
import os
from dotenv import load_dotenv
from core.cloudflare_api import CloudflareEmailAPI

# 加载环境变量
load_dotenv()

CLOUDFLARE_API_TOKEN = os.getenv('CLOUDFLARE_API_TOKEN')
CLOUDFLARE_ZONE_ID = os.getenv('CLOUDFLARE_ZONE_ID')
DOMAIN_NAME = os.getenv('DOMAIN_NAME')
DEFAULT_FORWARD_EMAIL = os.getenv('DEFAULT_FORWARD_EMAIL')

async def setup_complete_email_system():
    """设置完整的邮件系统"""
    print(f"🚀 开始为域名 {DOMAIN_NAME} 设置完整的邮件系统...")
    
    # 检查必要的环境变量
    if not all([CLOUDFLARE_API_TOKEN, CLOUDFLARE_ZONE_ID, DOMAIN_NAME, DEFAULT_FORWARD_EMAIL]):
        print("❌ 缺少必要的环境变量，请检查 .env 文件")
        print("需要的变量：CLOUDFLARE_API_TOKEN, CLOUDFLARE_ZONE_ID, DOMAIN_NAME, DEFAULT_FORWARD_EMAIL")
        return False
    
    try:
        # 初始化 Cloudflare API
        async with CloudflareEmailAPI(CLOUDFLARE_API_TOKEN) as api:
            print("✅ Cloudflare API 连接成功")
            
            # 1. 测试连接
            if not await api.test_connection():
                print("❌ Cloudflare API 连接测试失败")
                return False
            
            # 2. 启用邮件路由
            print("📧 启用邮件路由...")
            try:
                await api.enable_email_routing(CLOUDFLARE_ZONE_ID)
                print("✅ 邮件路由已启用")
            except Exception as e:
                if "already enabled" in str(e).lower():
                    print("✅ 邮件路由已经启用")
                else:
                    print(f"⚠️  启用邮件路由时出现问题: {e}")
            
            # 3. 创建目标邮箱地址
            print(f"📮 设置目标邮箱地址: {DEFAULT_FORWARD_EMAIL}")
            try:
                dest_result = await api.create_destination_address(CLOUDFLARE_ZONE_ID, DEFAULT_FORWARD_EMAIL)
                dest_id = dest_result.get('id')
                print(f"✅ 目标邮箱地址已创建: {dest_id}")
                
                # 发送验证邮件
                if dest_id:
                    await api.verify_destination_address(CLOUDFLARE_ZONE_ID, dest_id)
                    print(f"📧 验证邮件已发送到 {DEFAULT_FORWARD_EMAIL}")
                    print("⚠️  请检查您的邮箱并点击验证链接")
                    
            except Exception as e:
                if "already exists" in str(e).lower():
                    print("✅ 目标邮箱地址已存在")
                else:
                    print(f"❌ 创建目标邮箱地址失败: {e}")
                    return False
            
            # 4. 创建通配符转发规则
            print("🔄 创建通配符转发规则...")
            try:
                rule_result = await api.create_catch_all_rule(CLOUDFLARE_ZONE_ID, DEFAULT_FORWARD_EMAIL)
                rule_id = rule_result.get('id')
                print(f"✅ 通配符转发规则已创建: {rule_id}")
                print(f"📧 所有发送到 *@{DOMAIN_NAME} 的邮件将转发到 {DEFAULT_FORWARD_EMAIL}")
                
            except Exception as e:
                if "already exists" in str(e).lower():
                    print("✅ 通配符转发规则已存在")
                else:
                    print(f"❌ 创建通配符转发规则失败: {e}")
                    return False
            
            # 5. 获取邮件路由统计
            print("📊 获取邮件路由统计...")
            try:
                stats = await api.get_email_routing_stats(CLOUDFLARE_ZONE_ID)
                print(f"✅ 邮件路由状态: {'已启用' if stats.get('enabled') else '未启用'}")
                print(f"📋 转发规则数量: {stats.get('rules_count', 0)}")
                print(f"📮 目标地址数量: {stats.get('addresses_count', 0)}")
                
            except Exception as e:
                print(f"⚠️  获取统计信息失败: {e}")
            
            print("\n🎉 邮件系统设置完成！")
            print(f"✅ 域名: {DOMAIN_NAME}")
            print(f"✅ 转发到: {DEFAULT_FORWARD_EMAIL}")
            print(f"✅ 支持: 任意邮箱@{DOMAIN_NAME}")
            
            print("\n📝 使用说明:")
            print(f"1. 现在您可以使用任意前缀的邮箱地址，如: test@{DOMAIN_NAME}")
            print(f"2. 所有邮件都会自动转发到 {DEFAULT_FORWARD_EMAIL}")
            print(f"3. 请确保已验证目标邮箱地址（检查验证邮件）")
            print(f"4. 可以使用系统生成随机邮箱地址")
            
            return True
            
    except Exception as e:
        print(f"❌ 设置邮件系统时出现错误: {e}")
        return False

async def test_email_system():
    """测试邮件系统"""
    print(f"\n🧪 测试邮件系统...")
    
    try:
        async with CloudflareEmailAPI(CLOUDFLARE_API_TOKEN) as api:
            # 获取邮件路由状态
            stats = await api.get_email_routing_stats(CLOUDFLARE_ZONE_ID)
            
            if stats.get('enabled'):
                print("✅ 邮件路由已启用")
                print(f"📋 活跃规则: {stats.get('rules_count', 0)}")
                print(f"📮 目标地址: {stats.get('addresses_count', 0)}")
                
                # 显示规则详情
                rules = stats.get('rules', [])
                if rules:
                    print("\n📋 转发规则:")
                    for i, rule in enumerate(rules, 1):
                        print(f"  {i}. {rule.get('name', 'Unknown')} - {'启用' if rule.get('enabled') else '禁用'}")
                
                # 显示目标地址
                addresses = stats.get('addresses', [])
                if addresses:
                    print("\n📮 目标地址:")
                    for i, addr in enumerate(addresses, 1):
                        status = addr.get('verified', False)
                        print(f"  {i}. {addr.get('email', 'Unknown')} - {'已验证' if status else '未验证'}")
                
                return True
            else:
                print("❌ 邮件路由未启用")
                return False
                
    except Exception as e:
        print(f"❌ 测试邮件系统失败: {e}")
        return False

async def main():
    """主函数"""
    print("=" * 60)
    print("无限邮箱系统 - 邮件系统设置")
    print("=" * 60)
    
    # 设置邮件系统
    setup_success = await setup_complete_email_system()
    
    if setup_success:
        # 测试邮件系统
        await test_email_system()
        
        print("\n" + "=" * 60)
        print("🎉 邮件系统设置和测试完成！")
        print("现在您可以使用无限邮箱系统了。")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 邮件系统设置失败，请检查配置和网络连接。")
        print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
