#!/usr/bin/env python3
"""
简单的数据库测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import DatabaseManager

async def test_database():
    """测试数据库功能"""
    print("🗄️  测试数据库功能...")
    
    try:
        db_path = 'data/emails.db'
        db = DatabaseManager(db_path)
        await db.initialize()
        print("✅ 数据库初始化成功")
        
        # 测试统计信息
        stats = await db.get_statistics()
        print("✅ 数据库连接正常")
        print(f"📊 统计信息: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_database())
    if result:
        print("🎉 数据库测试通过！")
    else:
        print("❌ 数据库测试失败！")
