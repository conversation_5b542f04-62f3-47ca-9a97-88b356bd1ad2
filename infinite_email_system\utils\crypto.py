"""
加密解密工具
"""

from cryptography.fernet import Fernet
import base64
import os
import json
from typing import Union, Dict, Any
from utils.exceptions import CryptoError


class CryptoManager:
    """加密管理器"""
    
    def __init__(self, key_file: str = "secret.key"):
        self.key_file = key_file
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)
    
    def _get_or_create_key(self) -> bytes:
        """获取或创建加密密钥"""
        try:
            if os.path.exists(self.key_file):
                with open(self.key_file, 'rb') as f:
                    return f.read()
            else:
                key = Fernet.generate_key()
                with open(self.key_file, 'wb') as f:
                    f.write(key)
                return key
        except Exception as e:
            raise CryptoError(f"Failed to get or create encryption key: {e}")
    
    def encrypt(self, data: str) -> str:
        """加密数据"""
        try:
            if not isinstance(data, str):
                raise ValueError("Data must be a string")
            return self.cipher.encrypt(data.encode()).decode()
        except Exception as e:
            raise CryptoError(f"Encryption failed: {e}")
    
    def decrypt(self, encrypted_data: str) -> str:
        """解密数据"""
        try:
            if not isinstance(encrypted_data, str):
                raise ValueError("Encrypted data must be a string")
            return self.cipher.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            raise CryptoError(f"Decryption failed: {e}")
    
    def encrypt_config(self, config_dict: Dict[str, Any]) -> str:
        """加密配置字典"""
        try:
            json_str = json.dumps(config_dict, ensure_ascii=False)
            return self.encrypt(json_str)
        except Exception as e:
            raise CryptoError(f"Config encryption failed: {e}")
    
    def decrypt_config(self, encrypted_config: str) -> Dict[str, Any]:
        """解密配置字典"""
        try:
            json_str = self.decrypt(encrypted_config)
            return json.loads(json_str)
        except Exception as e:
            raise CryptoError(f"Config decryption failed: {e}")
    
    def encrypt_file(self, file_path: str, output_path: str = None) -> str:
        """加密文件"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            if output_path is None:
                output_path = file_path + ".encrypted"
            
            with open(file_path, 'rb') as f:
                data = f.read()
            
            encrypted_data = self.cipher.encrypt(data)
            
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)
            
            return output_path
        except Exception as e:
            raise CryptoError(f"File encryption failed: {e}")
    
    def decrypt_file(self, encrypted_file_path: str, output_path: str = None) -> str:
        """解密文件"""
        try:
            if not os.path.exists(encrypted_file_path):
                raise FileNotFoundError(f"Encrypted file not found: {encrypted_file_path}")
            
            if output_path is None:
                output_path = encrypted_file_path.replace(".encrypted", "")
            
            with open(encrypted_file_path, 'rb') as f:
                encrypted_data = f.read()
            
            data = self.cipher.decrypt(encrypted_data)
            
            with open(output_path, 'wb') as f:
                f.write(data)
            
            return output_path
        except Exception as e:
            raise CryptoError(f"File decryption failed: {e}")
    
    def generate_new_key(self) -> bytes:
        """生成新的加密密钥"""
        try:
            new_key = Fernet.generate_key()
            # 备份旧密钥
            if os.path.exists(self.key_file):
                backup_file = self.key_file + ".backup"
                os.rename(self.key_file, backup_file)
            
            # 保存新密钥
            with open(self.key_file, 'wb') as f:
                f.write(new_key)
            
            self.key = new_key
            self.cipher = Fernet(self.key)
            
            return new_key
        except Exception as e:
            raise CryptoError(f"Key generation failed: {e}")
    
    def verify_key(self) -> bool:
        """验证密钥是否有效"""
        try:
            test_data = "test_encryption"
            encrypted = self.encrypt(test_data)
            decrypted = self.decrypt(encrypted)
            return decrypted == test_data
        except:
            return False
