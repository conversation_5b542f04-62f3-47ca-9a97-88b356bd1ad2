#!/usr/bin/env python3
"""
DNS 记录配置脚本
为无限邮箱系统配置必要的 DNS 记录
"""

import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

CLOUDFLARE_API_TOKEN = os.getenv('CLOUDFLARE_API_TOKEN')
CLOUDFLARE_ZONE_ID = os.getenv('CLOUDFLARE_ZONE_ID')
DOMAIN_NAME = os.getenv('DOMAIN_NAME')

# CloudFlare API 基础 URL
CLOUDFLARE_API_BASE = "https://api.cloudflare.com/client/v4"

def get_headers():
    """获取 API 请求头"""
    return {
        'Authorization': f'Bearer {CLOUDFLARE_API_TOKEN}',
        'Content-Type': 'application/json'
    }

def create_dns_record(record_type, name, content, ttl=300, priority=None):
    """创建 DNS 记录"""
    url = f"{CLOUDFLARE_API_BASE}/zones/{CLOUDFLARE_ZONE_ID}/dns_records"
    
    data = {
        'type': record_type,
        'name': name,
        'content': content,
        'ttl': ttl
    }
    
    if priority is not None:
        data['priority'] = priority
    
    response = requests.post(url, headers=get_headers(), json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print(f"✅ 成功创建 {record_type} 记录: {name} -> {content}")
            return True
        else:
            print(f"❌ 创建 {record_type} 记录失败: {result['errors']}")
            return False
    else:
        print(f"❌ API 请求失败: {response.status_code} - {response.text}")
        return False

def get_existing_records():
    """获取现有的 DNS 记录"""
    url = f"{CLOUDFLARE_API_BASE}/zones/{CLOUDFLARE_ZONE_ID}/dns_records"
    response = requests.get(url, headers=get_headers())
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            return result['result']
    return []

def setup_email_dns():
    """设置邮件系统所需的 DNS 记录"""
    print(f"🚀 开始为域名 {DOMAIN_NAME} 配置 DNS 记录...")
    
    # 检查必要的环境变量
    if not all([CLOUDFLARE_API_TOKEN, CLOUDFLARE_ZONE_ID, DOMAIN_NAME]):
        print("❌ 缺少必要的环境变量，请检查 .env 文件")
        return False
    
    # 获取现有记录
    existing_records = get_existing_records()
    existing_names = {record['name'] for record in existing_records}
    
    print(f"📋 当前已存在的 DNS 记录: {len(existing_records)} 条")
    
    # 需要创建的 DNS 记录
    dns_records = [
        # MX 记录 - 邮件路由
        {
            'type': 'MX',
            'name': DOMAIN_NAME,
            'content': f'mail.{DOMAIN_NAME}',
            'priority': 10,
            'ttl': 300
        },
        # A 记录 - 邮件服务器（指向 Cloudflare 的邮件路由服务）
        {
            'type': 'A',
            'name': f'mail.{DOMAIN_NAME}',
            'content': '192.0.2.1',  # RFC5737 测试地址，Cloudflare 会处理邮件路由
            'ttl': 300
        },
        # TXT 记录 - SPF
        {
            'type': 'TXT',
            'name': DOMAIN_NAME,
            'content': f'v=spf1 a mx include:{DOMAIN_NAME} ~all',
            'ttl': 300
        },
        # TXT 记录 - DMARC
        {
            'type': 'TXT',
            'name': f'_dmarc.{DOMAIN_NAME}',
            'content': 'v=DMARC1; p=quarantine; rua=mailto:dmarc@' + DOMAIN_NAME,
            'ttl': 300
        },
        # CNAME 记录 - 通配符子域名
        {
            'type': 'CNAME',
            'name': f'*.{DOMAIN_NAME}',
            'content': DOMAIN_NAME,
            'ttl': 300
        }
    ]
    
    success_count = 0
    
    for record in dns_records:
        record_name = record['name']
        
        # 检查记录是否已存在
        if record_name in existing_names:
            print(f"⚠️  记录已存在，跳过: {record['type']} {record_name}")
            continue
        
        # 创建记录
        if record['type'] == 'MX':
            success = create_dns_record(
                record['type'], 
                record['name'], 
                record['content'], 
                record['ttl'], 
                record['priority']
            )
        else:
            success = create_dns_record(
                record['type'], 
                record['name'], 
                record['content'], 
                record['ttl']
            )
        
        if success:
            success_count += 1
    
    print(f"\n🎉 DNS 配置完成！成功创建 {success_count} 条新记录")
    print(f"📝 请注意：A 记录指向的是 127.0.0.1，部署到服务器时需要修改为实际 IP")
    
    return success_count > 0

if __name__ == "__main__":
    setup_email_dns()
