"""
邮件接收处理类
"""

import imaplib
import smtplib
import email
import re
import asyncio
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from utils.exceptions import EmailSystemException
from utils.logger import LoggerMixin
from utils.validators import EmailValidator


class EmailReceiver(LoggerMixin):
    """邮件接收处理类"""
    
    def __init__(self, 
                 imap_server: str = "imap.gmail.com",
                 imap_port: int = 993,
                 smtp_server: str = "smtp.gmail.com",
                 smtp_port: int = 587):
        self.imap_server = imap_server
        self.imap_port = imap_port
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.imap_connection = None
        self.smtp_connection = None
        self.logged_in = False
    
    async def connect_imap(self, email_address: str, password: str) -> bool:
        """连接IMAP服务器"""
        try:
            self.imap_connection = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.imap_connection.login(email_address, password)
            self.logged_in = True
            self.logger.info(f"Successfully connected to IMAP server: {email_address}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to IMAP server: {e}")
            return False
    
    async def connect_smtp(self, email_address: str, password: str) -> bool:
        """连接SMTP服务器"""
        try:
            self.smtp_connection = smtplib.SMTP(self.smtp_server, self.smtp_port)
            self.smtp_connection.starttls()
            self.smtp_connection.login(email_address, password)
            self.logger.info(f"Successfully connected to SMTP server: {email_address}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to SMTP server: {e}")
            return False
    
    async def get_recent_emails(self, 
                               folder: str = "INBOX",
                               limit: int = 10,
                               since_days: int = 1) -> List[Dict[str, Any]]:
        """获取最近的邮件"""
        try:
            if not self.logged_in or not self.imap_connection:
                raise EmailSystemException("Not connected to IMAP server")
            
            # 选择文件夹
            self.imap_connection.select(folder)
            
            # 计算日期范围
            since_date = (datetime.now() - timedelta(days=since_days)).strftime("%d-%b-%Y")
            
            # 搜索邮件
            search_criteria = f'(SINCE "{since_date}")'
            status, message_ids = self.imap_connection.search(None, search_criteria)
            
            if status != 'OK':
                raise EmailSystemException("Failed to search emails")
            
            # 获取邮件ID列表
            email_ids = message_ids[0].split()
            
            # 限制数量
            if limit > 0:
                email_ids = email_ids[-limit:]
            
            emails = []
            
            for email_id in email_ids:
                try:
                    # 获取邮件
                    status, msg_data = self.imap_connection.fetch(email_id, '(RFC822)')
                    
                    if status == 'OK':
                        # 解析邮件
                        email_message = email.message_from_bytes(msg_data[0][1])
                        parsed_email = await self._parse_email(email_message)
                        parsed_email['id'] = email_id.decode()
                        emails.append(parsed_email)
                        
                except Exception as e:
                    self.logger.warning(f"Failed to parse email {email_id}: {e}")
                    continue
            
            self.logger.info(f"Retrieved {len(emails)} emails from {folder}")
            return emails
            
        except Exception as e:
            raise EmailSystemException(f"Failed to get recent emails: {e}")
    
    async def _parse_email(self, email_message) -> Dict[str, Any]:
        """解析邮件内容"""
        try:
            # 基本信息
            parsed = {
                'subject': email_message.get('Subject', ''),
                'from': email_message.get('From', ''),
                'to': email_message.get('To', ''),
                'date': email_message.get('Date', ''),
                'message_id': email_message.get('Message-ID', ''),
                'content': '',
                'html_content': '',
                'attachments': []
            }
            
            # 解析邮件内容
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    
                    # 跳过附件
                    if "attachment" in content_disposition:
                        attachment_info = {
                            'filename': part.get_filename(),
                            'content_type': content_type,
                            'size': len(part.get_payload(decode=True)) if part.get_payload(decode=True) else 0
                        }
                        parsed['attachments'].append(attachment_info)
                        continue
                    
                    # 获取文本内容
                    if content_type == "text/plain":
                        parsed['content'] = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif content_type == "text/html":
                        parsed['html_content'] = part.get_payload(decode=True).decode('utf-8', errors='ignore')
            else:
                # 单部分邮件
                parsed['content'] = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
            
            return parsed
            
        except Exception as e:
            self.logger.error(f"Failed to parse email: {e}")
            return {
                'subject': 'Parse Error',
                'from': '',
                'to': '',
                'date': '',
                'content': f'Failed to parse email: {e}',
                'html_content': '',
                'attachments': []
            }
    
    async def search_emails_by_sender(self, sender: str, limit: int = 10) -> List[Dict[str, Any]]:
        """根据发件人搜索邮件"""
        try:
            if not self.logged_in or not self.imap_connection:
                raise EmailSystemException("Not connected to IMAP server")
            
            self.imap_connection.select("INBOX")
            
            # 搜索特定发件人的邮件
            search_criteria = f'(FROM "{sender}")'
            status, message_ids = self.imap_connection.search(None, search_criteria)
            
            if status != 'OK':
                raise EmailSystemException("Failed to search emails")
            
            email_ids = message_ids[0].split()
            
            if limit > 0:
                email_ids = email_ids[-limit:]
            
            emails = []
            for email_id in email_ids:
                try:
                    status, msg_data = self.imap_connection.fetch(email_id, '(RFC822)')
                    if status == 'OK':
                        email_message = email.message_from_bytes(msg_data[0][1])
                        parsed_email = await self._parse_email(email_message)
                        parsed_email['id'] = email_id.decode()
                        emails.append(parsed_email)
                except Exception as e:
                    self.logger.warning(f"Failed to parse email {email_id}: {e}")
                    continue
            
            return emails
            
        except Exception as e:
            raise EmailSystemException(f"Failed to search emails by sender: {e}")
    
    async def extract_verification_codes(self, emails: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """从邮件中提取验证码"""
        try:
            verification_patterns = [
                r'验证码[：:]\s*(\d{4,8})',
                r'verification code[：:]\s*(\d{4,8})',
                r'code[：:]\s*(\d{4,8})',
                r'(\d{6})\s*is your verification code',
                r'Your code is[：:]\s*(\d{4,8})',
                r'验证码为[：:]\s*(\d{4,8})',
                r'激活码[：:]\s*(\d{4,8})',
                r'动态码[：:]\s*(\d{4,8})'
            ]
            
            results = []
            
            for email_data in emails:
                content = email_data.get('content', '') + ' ' + email_data.get('html_content', '')
                
                for pattern in verification_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        for code in matches:
                            results.append({
                                'email_id': email_data.get('id'),
                                'subject': email_data.get('subject'),
                                'from': email_data.get('from'),
                                'date': email_data.get('date'),
                                'verification_code': code,
                                'pattern_used': pattern
                            })
                        break  # 找到验证码后跳出循环
            
            self.logger.info(f"Extracted {len(results)} verification codes")
            return results
            
        except Exception as e:
            raise EmailSystemException(f"Failed to extract verification codes: {e}")
    
    async def send_test_email(self, 
                             to_email: str,
                             subject: str = "Test Email",
                             content: str = "This is a test email.") -> bool:
        """发送测试邮件"""
        try:
            if not self.smtp_connection:
                raise EmailSystemException("Not connected to SMTP server")
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['Subject'] = subject
            msg['To'] = to_email
            
            # 添加内容
            msg.attach(MIMEText(content, 'plain'))
            
            # 发送邮件
            self.smtp_connection.send_message(msg)
            
            self.logger.info(f"Test email sent to: {to_email}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send test email: {e}")
            return False
    
    async def get_folder_list(self) -> List[str]:
        """获取邮箱文件夹列表"""
        try:
            if not self.logged_in or not self.imap_connection:
                raise EmailSystemException("Not connected to IMAP server")
            
            status, folders = self.imap_connection.list()
            
            if status != 'OK':
                raise EmailSystemException("Failed to get folder list")
            
            folder_names = []
            for folder in folders:
                # 解析文件夹名称
                folder_str = folder.decode()
                # 提取文件夹名称（通常在最后的引号中）
                if '"' in folder_str:
                    folder_name = folder_str.split('"')[-2]
                    folder_names.append(folder_name)
            
            return folder_names
            
        except Exception as e:
            raise EmailSystemException(f"Failed to get folder list: {e}")
    
    async def mark_as_read(self, email_id: str) -> bool:
        """标记邮件为已读"""
        try:
            if not self.logged_in or not self.imap_connection:
                raise EmailSystemException("Not connected to IMAP server")
            
            self.imap_connection.store(email_id, '+FLAGS', '\\Seen')
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to mark email as read: {e}")
            return False
    
    async def delete_email(self, email_id: str) -> bool:
        """删除邮件"""
        try:
            if not self.logged_in or not self.imap_connection:
                raise EmailSystemException("Not connected to IMAP server")
            
            self.imap_connection.store(email_id, '+FLAGS', '\\Deleted')
            self.imap_connection.expunge()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete email: {e}")
            return False
    
    async def get_email_statistics(self) -> Dict[str, Any]:
        """获取邮箱统计信息"""
        try:
            if not self.logged_in or not self.imap_connection:
                raise EmailSystemException("Not connected to IMAP server")
            
            stats = {}
            
            # 获取收件箱统计
            self.imap_connection.select("INBOX")
            status, messages = self.imap_connection.search(None, 'ALL')
            stats['total_emails'] = len(messages[0].split()) if messages[0] else 0
            
            # 获取未读邮件数
            status, unread = self.imap_connection.search(None, 'UNSEEN')
            stats['unread_emails'] = len(unread[0].split()) if unread[0] else 0
            
            # 获取今日邮件数
            today = datetime.now().strftime("%d-%b-%Y")
            status, today_emails = self.imap_connection.search(None, f'(SINCE "{today}")')
            stats['today_emails'] = len(today_emails[0].split()) if today_emails[0] else 0
            
            return stats
            
        except Exception as e:
            raise EmailSystemException(f"Failed to get email statistics: {e}")
    
    async def disconnect(self):
        """断开连接"""
        try:
            if self.imap_connection:
                self.imap_connection.logout()
                self.imap_connection = None
            
            if self.smtp_connection:
                self.smtp_connection.quit()
                self.smtp_connection = None
            
            self.logged_in = False
            self.logger.info("Disconnected from email servers")
            
        except Exception as e:
            self.logger.error(f"Error during disconnect: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            if self.imap_connection:
                self.imap_connection.logout()
            if self.smtp_connection:
                self.smtp_connection.quit()
        except:
            pass
