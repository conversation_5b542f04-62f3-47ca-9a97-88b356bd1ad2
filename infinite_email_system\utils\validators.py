"""
数据验证工具
"""

import re
import string
from typing import List, Optional
from utils.exceptions import ValidationError


class EmailValidator:
    """邮箱验证器"""
    
    # 邮箱正则表达式
    EMAIL_PATTERN = re.compile(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    )
    
    @classmethod
    def validate_email(cls, email: str) -> bool:
        """验证邮箱格式"""
        if not isinstance(email, str):
            return False
        
        if len(email) > 254:  # RFC 5321 限制
            return False
        
        return bool(cls.EMAIL_PATTERN.match(email))
    
    @classmethod
    def validate_email_prefix(cls, prefix: str) -> bool:
        """验证邮箱前缀"""
        if not isinstance(prefix, str):
            return False
        
        if len(prefix) == 0 or len(prefix) > 64:  # RFC 5321 限制
            return False
        
        # 不能以点开始或结束
        if prefix.startswith('.') or prefix.endswith('.'):
            return False
        
        # 不能有连续的点
        if '..' in prefix:
            return False
        
        # 允许的字符
        allowed_chars = string.ascii_letters + string.digits + '._+-'
        return all(c in allowed_chars for c in prefix)
    
    @classmethod
    def validate_batch_emails(cls, emails: List[str]) -> List[str]:
        """批量验证邮箱，返回有效的邮箱列表"""
        valid_emails = []
        for email in emails:
            if cls.validate_email(email):
                valid_emails.append(email)
        return valid_emails


class DomainValidator:
    """域名验证器"""
    
    # 域名正则表达式
    DOMAIN_PATTERN = re.compile(
        r'^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$'
    )
    
    # 有效的TLD列表
    VALID_TLDS = ['.tk', '.ml', '.ga', '.cf', '.com', '.net', '.org', '.edu', '.gov']
    
    @classmethod
    def validate_domain(cls, domain: str) -> bool:
        """验证域名格式"""
        if not isinstance(domain, str):
            return False
        
        if len(domain) > 253:  # RFC 1035 限制
            return False
        
        if not cls.DOMAIN_PATTERN.match(domain):
            return False
        
        # 检查TLD
        tld = '.' + domain.split('.')[-1] if '.' in domain else ''
        return tld.lower() in [t.lower() for t in cls.VALID_TLDS]
    
    @classmethod
    def validate_domain_prefix(cls, prefix: str) -> bool:
        """验证域名前缀"""
        if not isinstance(prefix, str):
            return False
        
        if len(prefix) == 0 or len(prefix) > 63:  # RFC 1035 限制
            return False
        
        # 不能以连字符开始或结束
        if prefix.startswith('-') or prefix.endswith('-'):
            return False
        
        # 只能包含字母、数字和连字符
        return re.match(r'^[a-zA-Z0-9-]+$', prefix) is not None
    
    @classmethod
    def validate_tld(cls, tld: str) -> bool:
        """验证TLD"""
        if not isinstance(tld, str):
            return False
        
        if not tld.startswith('.'):
            tld = '.' + tld
        
        return tld.lower() in [t.lower() for t in cls.VALID_TLDS]


class ConfigValidator:
    """配置验证器"""
    
    @classmethod
    def validate_api_token(cls, token: str) -> bool:
        """验证API令牌格式"""
        if not isinstance(token, str):
            return False

        # Cloudflare API令牌通常是40个字符的字母数字字符串
        if len(token) != 40:
            return False

        return re.match(r'^[a-zA-Z0-9]{40}$', token) is not None
    
    @classmethod
    def validate_zone_id(cls, zone_id: str) -> bool:
        """验证Zone ID格式"""
        if not isinstance(zone_id, str):
            return False
        
        # Cloudflare Zone ID通常是32个字符的十六进制字符串
        if len(zone_id) != 32:
            return False
        
        return re.match(r'^[a-fA-F0-9]{32}$', zone_id) is not None
    
    @classmethod
    def validate_url(cls, url: str) -> bool:
        """验证URL格式"""
        if not isinstance(url, str):
            return False
        
        url_pattern = re.compile(
            r'^https?://'  # http:// 或 https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # 域名
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP地址
            r'(?::\d+)?'  # 可选端口
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        return bool(url_pattern.match(url))


def validate_required_fields(data: dict, required_fields: List[str]) -> None:
    """验证必需字段"""
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == '':
            missing_fields.append(field)
    
    if missing_fields:
        raise ValidationError(f"Missing required fields: {', '.join(missing_fields)}")


def validate_field_types(data: dict, field_types: dict) -> None:
    """验证字段类型"""
    type_errors = []
    for field, expected_type in field_types.items():
        if field in data and not isinstance(data[field], expected_type):
            type_errors.append(f"{field} should be {expected_type.__name__}")
    
    if type_errors:
        raise ValidationError(f"Type validation errors: {', '.join(type_errors)}")


def sanitize_string(text: str, max_length: Optional[int] = None) -> str:
    """清理字符串"""
    if not isinstance(text, str):
        return str(text)
    
    # 移除前后空白
    text = text.strip()
    
    # 限制长度
    if max_length and len(text) > max_length:
        text = text[:max_length]
    
    return text
