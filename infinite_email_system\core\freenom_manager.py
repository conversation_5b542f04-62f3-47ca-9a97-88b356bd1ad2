
"""
Freenom自动化管理
"""

import asyncio
import time
from typing import Dict, Any, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

from utils.exceptions import FreenomError
from utils.logger import LoggerMixin
from config.constants import FREENOM_BASE_URL


class FreenomManager(LoggerMixin):
    """Freenom自动化操作类"""
    
    def __init__(self, headless: bool = True, timeout: int = 30):
        self.driver = None
        self.headless = headless
        self.timeout = timeout
        self.base_url = FREENOM_BASE_URL
        self._setup_driver()
    
    def _setup_driver(self):
        """设置WebDriver"""
        try:
            options = Options()
            
            if self.headless:
                options.add_argument('--headless')
            
            # 通用选项
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 设置用户代理
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            # 自动下载并设置ChromeDriver
            driver_path = ChromeDriverManager().install()
            self.driver = webdriver.Chrome(executable_path=driver_path, options=options)
            
            # 设置隐式等待
            self.driver.implicitly_wait(10)
            
            # 执行脚本隐藏自动化特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info("WebDriver setup completed")
            
        except Exception as e:
            raise FreenomError(f"Failed to setup WebDriver: {e}")
    
    async def check_domain_availability(self, domain: str) -> bool:
        """
        检查域名可用性
        
        Args:
            domain: 域名
        
        Returns:
            是否可用
        """
        try:
            self.logger.info(f"Checking availability for domain: {domain}")
            
            # 访问Freenom首页
            self.driver.get(f"{self.base_url}/en/index.html")
            
            # 等待页面加载
            await asyncio.sleep(2)
            
            # 查找搜索框
            search_box = WebDriverWait(self.driver, self.timeout).until(
                EC.presence_of_element_located((By.NAME, "domain"))
            )
            
            # 清空并输入域名
            search_box.clear()
            search_box.send_keys(domain)
            
            # 点击搜索按钮
            search_button = self.driver.find_element(By.XPATH, "//input[@type='submit' and @value='Check Availability']")
            search_button.click()
            
            # 等待结果页面加载
            WebDriverWait(self.driver, self.timeout).until(
                EC.presence_of_element_located((By.CLASS_NAME, "domain-checker-container"))
            )
            
            # 等待结果显示
            await asyncio.sleep(3)
            
            # 检查是否可用
            try:
                # 查找"Available"文本
                available_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Available')]")
                if available_elements:
                    self.logger.info(f"Domain {domain} is available")
                    return True
                
                # 查找"Get it now!"按钮
                get_now_buttons = self.driver.find_elements(By.XPATH, "//a[contains(text(), 'Get it now!')]")
                if get_now_buttons:
                    self.logger.info(f"Domain {domain} is available")
                    return True
                
                # 查找"Unavailable"或"Taken"文本
                unavailable_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Unavailable') or contains(text(), 'Taken')]")
                if unavailable_elements:
                    self.logger.info(f"Domain {domain} is not available")
                    return False
                
                # 如果没有明确的可用性指示，尝试其他方法
                page_source = self.driver.page_source.lower()
                if 'available' in page_source and domain.lower() in page_source:
                    return True
                elif 'unavailable' in page_source or 'taken' in page_source:
                    return False
                
                # 默认返回不可用
                self.logger.warning(f"Could not determine availability for {domain}, assuming unavailable")
                return False
                
            except NoSuchElementException:
                self.logger.warning(f"Could not find availability indicator for {domain}")
                return False
            
        except TimeoutException:
            self.logger.error(f"Timeout while checking domain availability: {domain}")
            return False
        except Exception as e:
            self.logger.error(f"Error checking domain availability: {e}")
            return False
    
    async def register_domain(self, domain: str, user_info: Dict[str, Any]) -> bool:
        """
        注册域名
        
        Args:
            domain: 域名
            user_info: 用户信息
        
        Returns:
            注册是否成功
        """
        try:
            self.logger.info(f"Attempting to register domain: {domain}")
            
            # 首先检查可用性
            is_available = await self.check_domain_availability(domain)
            if not is_available:
                raise FreenomError(f"Domain {domain} is not available")
            
            # 点击"Get it now!"按钮
            try:
                get_button = WebDriverWait(self.driver, self.timeout).until(
                    EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Get it now!')]"))
                )
                get_button.click()
            except TimeoutException:
                raise FreenomError("Could not find 'Get it now!' button")
            
            # 等待页面跳转
            await asyncio.sleep(3)
            
            # 选择免费期限（通常是12个月）
            try:
                period_select = WebDriverWait(self.driver, self.timeout).until(
                    EC.presence_of_element_located((By.NAME, "period"))
                )
                # 选择12个月免费
                period_select.send_keys("12 Months @ FREE")
            except TimeoutException:
                self.logger.warning("Could not find period selection, continuing...")
            
            # 点击继续按钮
            try:
                continue_button = WebDriverWait(self.driver, self.timeout).until(
                    EC.element_to_be_clickable((By.XPATH, "//input[@value='Continue']"))
                )
                continue_button.click()
            except TimeoutException:
                raise FreenomError("Could not find continue button")
            
            # 等待用户信息页面
            await asyncio.sleep(3)
            
            # 填写用户信息
            await self._fill_user_info(user_info)
            
            # 提交注册
            try:
                submit_button = WebDriverWait(self.driver, self.timeout).until(
                    EC.element_to_be_clickable((By.XPATH, "//input[@value='Complete Order']"))
                )
                submit_button.click()
            except TimeoutException:
                raise FreenomError("Could not find submit button")
            
            # 等待确认页面
            try:
                WebDriverWait(self.driver, self.timeout).until(
                    EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Order Confirmation') or contains(text(), 'Thank you')]"))
                )
                self.logger.info(f"Successfully registered domain: {domain}")
                return True
            except TimeoutException:
                # 检查是否有错误信息
                error_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Error') or contains(text(), 'Failed')]")
                if error_elements:
                    error_text = error_elements[0].text
                    raise FreenomError(f"Registration failed: {error_text}")
                else:
                    raise FreenomError("Registration status unclear")
            
        except Exception as e:
            self.logger.error(f"Failed to register domain {domain}: {e}")
            return False
    
    async def _fill_user_info(self, user_info: Dict[str, Any]):
        """填写用户信息"""
        try:
            # 默认用户信息
            default_info = {
                'first_name': '张',
                'last_name': '老二',
                'email': '<EMAIL>',
                'address': '世外桃源',
                'city': '桃子',
                'zipcode': '100000',
                'phone': '+8613800138000',
                'country': 'CN'
            }
            
            # 合并用户提供的信息
            info = {**default_info, **user_info}
            
            # 字段映射
            field_mapping = {
                'firstname': info['first_name'],
                'lastname': info['last_name'],
                'email': info['email'],
                'address': info['address'],
                'city': info['city'],
                'zipcode': info['zipcode'],
                'phonenumber': info['phone']
            }
            
            # 填写表单字段
            for field_name, value in field_mapping.items():
                try:
                    field = self.driver.find_element(By.NAME, field_name)
                    field.clear()
                    field.send_keys(str(value))
                    await asyncio.sleep(0.5)  # 小延迟避免过快输入
                except NoSuchElementException:
                    self.logger.warning(f"Field {field_name} not found, skipping...")
                    continue
            
            # 处理国家选择
            try:
                country_select = self.driver.find_element(By.NAME, "country")
                country_select.send_keys(info['country'])
            except NoSuchElementException:
                self.logger.warning("Country field not found, skipping...")
            
            # 同意条款
            try:
                terms_checkbox = self.driver.find_element(By.NAME, "tos")
                if not terms_checkbox.is_selected():
                    terms_checkbox.click()
            except NoSuchElementException:
                self.logger.warning("Terms checkbox not found, skipping...")
            
            self.logger.info("User information filled successfully")
            
        except Exception as e:
            raise FreenomError(f"Failed to fill user information: {e}")
    
    def take_screenshot(self, filename: str = None) -> str:
        """截图"""
        try:
            if filename is None:
                filename = f"freenom_screenshot_{int(time.time())}.png"
            
            self.driver.save_screenshot(filename)
            self.logger.info(f"Screenshot saved: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Failed to take screenshot: {e}")
            return ""
    
    def get_page_source(self) -> str:
        """获取页面源码"""
        try:
            return self.driver.page_source
        except Exception as e:
            self.logger.error(f"Failed to get page source: {e}")
            return ""
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.logger.info("WebDriver cleaned up")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def __del__(self):
        """析构函数"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass



