"""
域名管理核心类
"""

import random
import string
import time
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from utils.helpers import generate_random_string, format_timestamp
from utils.validators import DomainValidator
from utils.exceptions import DomainRegistrationError
from utils.logger import LoggerMixin
from config.constants import AVAILABLE_TLDS, MAX_DAILY_REGISTRATIONS
from core.freenom_manager import FreenomManager


class DomainManager(LoggerMixin):
    """域名管理核心类"""
    
    def __init__(self):
        self.freenom_manager = None
        self.available_tlds = AVAILABLE_TLDS.copy()
        self.daily_registration_count = 0
        self.last_registration_date = None
        self._reset_daily_counter()
    
    def _reset_daily_counter(self):
        """重置每日计数器"""
        today = datetime.now().date()
        if self.last_registration_date != today:
            self.daily_registration_count = 0
            self.last_registration_date = today
    
    async def generate_random_domain(self, 
                                   length: int = 8,
                                   include_timestamp: bool = True,
                                   preferred_tld: Optional[str] = None) -> str:
        """
        生成随机域名
        
        Args:
            length: 域名前缀长度
            include_timestamp: 是否包含时间戳
            preferred_tld: 首选TLD
        
        Returns:
            随机域名
        """
        try:
            # 生成前缀
            if include_timestamp:
                # 使用时间戳确保唯一性
                timestamp = str(int(time.time()))[-4:]  # 取后4位
                prefix_length = max(4, length - 4)
                prefix = generate_random_string(
                    prefix_length, 
                    include_letters=True, 
                    include_digits=True,
                    include_symbols=False,
                    exclude_ambiguous=True
                )
                domain_prefix = prefix + timestamp
            else:
                domain_prefix = generate_random_string(
                    length,
                    include_letters=True,
                    include_digits=True,
                    include_symbols=False,
                    exclude_ambiguous=True
                )
            
            # 确保首字符是字母
            if domain_prefix[0].isdigit():
                domain_prefix = random.choice(string.ascii_lowercase) + domain_prefix[1:]
            
            # 选择TLD
            if preferred_tld and preferred_tld in self.available_tlds:
                tld = preferred_tld
            else:
                tld = random.choice(self.available_tlds)
            
            domain = f"{domain_prefix}{tld}"
            
            # 验证域名格式
            if not DomainValidator.validate_domain(domain):
                raise DomainRegistrationError(f"Generated invalid domain: {domain}")
            
            self.logger.debug(f"Generated domain: {domain}")
            return domain
            
        except Exception as e:
            raise DomainRegistrationError(f"Failed to generate domain: {e}")
    
    async def generate_batch_domains(self, 
                                   count: int,
                                   length: int = 8,
                                   ensure_unique: bool = True) -> List[str]:
        """
        批量生成域名
        
        Args:
            count: 生成数量
            length: 域名前缀长度
            ensure_unique: 是否确保唯一性
        
        Returns:
            域名列表
        """
        try:
            if count > MAX_DAILY_REGISTRATIONS:
                raise DomainRegistrationError(f"Batch size exceeds daily limit: {MAX_DAILY_REGISTRATIONS}")
            
            domains = []
            generated_prefixes = set()
            max_attempts = count * 10
            attempts = 0
            
            while len(domains) < count and attempts < max_attempts:
                attempts += 1
                
                domain = await self.generate_random_domain(length, include_timestamp=True)
                domain_prefix = domain.split('.')[0]
                
                if ensure_unique and domain_prefix in generated_prefixes:
                    continue
                
                domains.append(domain)
                if ensure_unique:
                    generated_prefixes.add(domain_prefix)
                
                # 添加小延迟避免生成相同的时间戳
                await asyncio.sleep(0.1)
            
            self.logger.info(f"Generated {len(domains)} domains")
            return domains
            
        except Exception as e:
            raise DomainRegistrationError(f"Failed to generate batch domains: {e}")
    
    async def check_domain_availability(self, domain: str) -> Dict[str, Any]:
        """
        检查域名可用性
        
        Args:
            domain: 域名
        
        Returns:
            可用性信息
        """
        try:
            if not DomainValidator.validate_domain(domain):
                return {
                    'domain': domain,
                    'available': False,
                    'error': 'Invalid domain format'
                }
            
            # 初始化Freenom管理器
            if self.freenom_manager is None:
                self.freenom_manager = FreenomManager()
            
            # 检查可用性
            is_available = await self.freenom_manager.check_domain_availability(domain)
            
            result = {
                'domain': domain,
                'available': is_available,
                'checked_at': format_timestamp(),
                'tld': '.' + domain.split('.')[-1]
            }
            
            if is_available:
                self.logger.info(f"Domain {domain} is available")
            else:
                self.logger.info(f"Domain {domain} is not available")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to check domain availability: {e}")
            return {
                'domain': domain,
                'available': False,
                'error': str(e)
            }
    
    async def check_batch_availability(self, domains: List[str]) -> List[Dict[str, Any]]:
        """批量检查域名可用性"""
        try:
            results = []
            
            # 限制并发数量
            semaphore = asyncio.Semaphore(3)
            
            async def check_single_domain(domain):
                async with semaphore:
                    return await self.check_domain_availability(domain)
            
            # 并发检查
            tasks = [check_single_domain(domain) for domain in domains]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        'domain': domains[i],
                        'available': False,
                        'error': str(result)
                    })
                else:
                    processed_results.append(result)
            
            return processed_results
            
        except Exception as e:
            raise DomainRegistrationError(f"Failed to check batch availability: {e}")
    
    async def register_domain(self, domain: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        注册域名
        
        Args:
            domain: 域名
            user_info: 用户信息
        
        Returns:
            注册结果
        """
        try:
            # 检查每日限制
            self._reset_daily_counter()
            if self.daily_registration_count >= MAX_DAILY_REGISTRATIONS:
                raise DomainRegistrationError(f"Daily registration limit reached: {MAX_DAILY_REGISTRATIONS}")
            
            # 验证域名
            if not DomainValidator.validate_domain(domain):
                raise DomainRegistrationError(f"Invalid domain: {domain}")
            
            # 检查可用性
            availability = await self.check_domain_availability(domain)
            if not availability['available']:
                raise DomainRegistrationError(f"Domain not available: {domain}")
            
            # 初始化Freenom管理器
            if self.freenom_manager is None:
                self.freenom_manager = FreenomManager()
            
            # 执行注册
            registration_result = await self.freenom_manager.register_domain(domain, user_info)
            
            if registration_result:
                self.daily_registration_count += 1
                self.logger.info(f"Successfully registered domain: {domain}")
                
                return {
                    'domain': domain,
                    'success': True,
                    'registered_at': format_timestamp(),
                    'expires_at': format_timestamp(time.time() + 365 * 24 * 3600),  # 1年后
                    'user_info': user_info
                }
            else:
                raise DomainRegistrationError(f"Registration failed for domain: {domain}")
                
        except Exception as e:
            self.logger.error(f"Failed to register domain {domain}: {e}")
            return {
                'domain': domain,
                'success': False,
                'error': str(e)
            }
    
    async def get_domain_status(self, domain: str) -> Dict[str, Any]:
        """
        获取域名状态
        
        Args:
            domain: 域名
        
        Returns:
            域名状态信息
        """
        try:
            # 这里可以实现域名状态查询逻辑
            # 目前返回基本信息
            return {
                'domain': domain,
                'status': 'unknown',
                'checked_at': format_timestamp(),
                'message': 'Status check not implemented'
            }
            
        except Exception as e:
            return {
                'domain': domain,
                'status': 'error',
                'error': str(e)
            }
    
    async def renew_domain(self, domain: str) -> bool:
        """
        续费域名
        
        Args:
            domain: 域名
        
        Returns:
            续费是否成功
        """
        try:
            # 这里可以实现域名续费逻辑
            self.logger.info(f"Domain renewal not implemented for: {domain}")
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to renew domain {domain}: {e}")
            return False
    
    def get_available_tlds(self) -> List[str]:
        """获取可用的TLD列表"""
        return self.available_tlds.copy()
    
    def add_custom_tld(self, tld: str) -> bool:
        """添加自定义TLD"""
        try:
            if not tld.startswith('.'):
                tld = '.' + tld
            
            if DomainValidator.validate_tld(tld) and tld not in self.available_tlds:
                self.available_tlds.append(tld)
                self.logger.info(f"Added custom TLD: {tld}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to add custom TLD {tld}: {e}")
            return False
    
    def remove_tld(self, tld: str) -> bool:
        """移除TLD"""
        try:
            if not tld.startswith('.'):
                tld = '.' + tld
            
            if tld in self.available_tlds:
                self.available_tlds.remove(tld)
                self.logger.info(f"Removed TLD: {tld}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to remove TLD {tld}: {e}")
            return False
    
    def get_registration_stats(self) -> Dict[str, Any]:
        """获取注册统计信息"""
        self._reset_daily_counter()
        
        return {
            'daily_registrations': self.daily_registration_count,
            'daily_limit': MAX_DAILY_REGISTRATIONS,
            'remaining_today': MAX_DAILY_REGISTRATIONS - self.daily_registration_count,
            'last_registration_date': self.last_registration_date.isoformat() if self.last_registration_date else None,
            'available_tlds': len(self.available_tlds)
        }
    
    async def cleanup(self):
        """清理资源"""
        if self.freenom_manager:
            await self.freenom_manager.cleanup()
