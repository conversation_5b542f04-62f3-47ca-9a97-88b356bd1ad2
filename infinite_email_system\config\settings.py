"""
配置管理类
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union, List
from dotenv import load_dotenv

from utils.crypto import CryptoManager
from utils.helpers import safe_json_load, safe_json_save, ensure_directory
from utils.exceptions import ConfigurationError
from config.constants import *


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json", env_file: str = ".env"):
        self.config_file = Path(config_file)
        self.env_file = Path(env_file)
        self.crypto = CryptoManager()
        
        # 加载环境变量
        if self.env_file.exists():
            load_dotenv(self.env_file)
        
        # 加载配置
        self._config = self._load_config()
        
        # 确保必要目录存在
        self._ensure_directories()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = self._get_default_config()
        
        if not self.config_file.exists():
            # 创建默认配置文件
            safe_json_save(default_config, self.config_file)
            return default_config
        
        try:
            config = safe_json_load(self.config_file, default_config)
            # 合并默认配置，确保所有必要的键都存在
            return self._merge_configs(default_config, config)
        except Exception as e:
            raise ConfigurationError(f"Failed to load config: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "app": {
                "name": APP_NAME,
                "version": APP_VERSION,
                "debug": False,
                "log_level": "INFO"
            },
            "cloudflare": {
                "api_token": "",
                "default_zone_id": "",
                "rate_limit": CLOUDFLARE_RATE_LIMIT
            },
            "domain": {
                "name": "cfish22.dpdns.org",
                "zone_id": "733e04386ef1d5d5ffee19a6f74e8b07"
            },
            "freenom": {
                "base_url": FREENOM_BASE_URL,
                "available_tlds": AVAILABLE_TLDS,
                "max_daily_registrations": MAX_DAILY_REGISTRATIONS,
                "headless_mode": True
            },
            "email": {
                "default_forward_to": "",
                "batch_size": 10,
                "auto_cleanup": True,
                "cleanup_days": EMAIL_CLEANUP_DAYS
            },
            "database": {
                "path": f"{DATA_DIR}/{DB_NAME}",
                "backup_enabled": True,
                "backup_interval_hours": 24
            },
            "ui": {
                "theme": "light",
                "language": "zh_CN",
                "auto_save": True,
                "window_size": DEFAULT_WINDOW_SIZE
            },
            "security": {
                "encrypt_sensitive_data": True,
                "api_timeout": API_TIMEOUT,
                "max_concurrent_requests": MAX_CONCURRENT_REQUESTS
            }
        }
    
    def _merge_configs(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        result = default.copy()
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _ensure_directories(self):
        """确保必要目录存在"""
        directories = [DATA_DIR, LOGS_DIR, CONFIG_DIR, RESOURCES_DIR]
        for directory in directories:
            ensure_directory(directory)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键 (如 'app.name')
            default: 默认值
        
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            # 检查环境变量覆盖
            env_key = key.upper().replace('.', '_')
            env_value = os.getenv(env_key)
            if env_value is not None:
                # 尝试转换类型
                if isinstance(value, bool):
                    return env_value.lower() in ('true', '1', 'yes', 'on')
                elif isinstance(value, int):
                    try:
                        return int(env_value)
                    except ValueError:
                        pass
                elif isinstance(value, float):
                    try:
                        return float(env_value)
                    except ValueError:
                        pass
                return env_value
            
            return value
        except Exception:
            return default
    
    def set(self, key: str, value: Any, encrypt: bool = False) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            encrypt: 是否加密存储
        """
        try:
            keys = key.split('.')
            config = self._config
            
            # 导航到目标位置
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置值
            if encrypt and isinstance(value, str):
                value = self.crypto.encrypt(value)
                # 标记为加密
                encrypted_key = f"_encrypted_{keys[-1]}"
                config[encrypted_key] = True
            
            config[keys[-1]] = value
            
        except Exception as e:
            raise ConfigurationError(f"Failed to set config: {e}")
    
    def get_encrypted(self, key: str, default: Any = None) -> Any:
        """获取加密的配置值"""
        try:
            encrypted_value = self.get(key, default)
            if encrypted_value is None or encrypted_value == default:
                return default
            
            # 检查是否标记为加密
            keys = key.split('.')
            encrypted_key = f"_encrypted_{keys[-1]}"
            is_encrypted = self.get('.'.join(keys[:-1] + [encrypted_key]), False)
            
            if is_encrypted and isinstance(encrypted_value, str):
                return self.crypto.decrypt(encrypted_value)
            
            return encrypted_value
        except Exception:
            return default
    
    def save(self) -> bool:
        """保存配置到文件"""
        try:
            return safe_json_save(self._config, self.config_file)
        except Exception as e:
            raise ConfigurationError(f"Failed to save config: {e}")
    
    def reload(self) -> None:
        """重新加载配置"""
        self._config = self._load_config()
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self._config = self._get_default_config()
        self.save()
    
    def export_config(self, file_path: str, include_sensitive: bool = False) -> bool:
        """导出配置"""
        try:
            config_to_export = self._config.copy()
            
            if not include_sensitive:
                # 移除敏感信息
                sensitive_keys = [
                    'cloudflare.api_token',
                    'database.encryption_key'
                ]
                
                for key in sensitive_keys:
                    keys = key.split('.')
                    config = config_to_export
                    for k in keys[:-1]:
                        if k in config:
                            config = config[k]
                        else:
                            break
                    else:
                        if keys[-1] in config:
                            config[keys[-1]] = "***HIDDEN***"
            
            return safe_json_save(config_to_export, file_path)
        except Exception:
            return False
    
    def import_config(self, file_path: str) -> bool:
        """导入配置"""
        try:
            imported_config = safe_json_load(file_path)
            if imported_config is None:
                return False
            
            # 合并配置
            self._config = self._merge_configs(self._config, imported_config)
            return self.save()
        except Exception:
            return False
    
    def validate_config(self) -> List[str]:
        """验证配置，返回错误列表"""
        errors = []
        
        # 验证必需的配置项
        required_configs = [
            'app.name',
            'app.version',
            'database.path'
        ]
        
        for config_key in required_configs:
            if self.get(config_key) is None:
                errors.append(f"Missing required config: {config_key}")
        
        # 验证Cloudflare配置
        api_token = self.get_encrypted('cloudflare.api_token')
        if api_token and len(api_token) != 40:
            errors.append("Invalid Cloudflare API token format")
        
        # 验证数据库路径
        db_path = self.get('database.path')
        if db_path:
            db_dir = Path(db_path).parent
            if not db_dir.exists():
                try:
                    ensure_directory(db_dir)
                except Exception:
                    errors.append(f"Cannot create database directory: {db_dir}")
        
        return errors
    
    @property
    def config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self._config.copy()
