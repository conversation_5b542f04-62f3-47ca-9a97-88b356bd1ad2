"""
通用辅助函数
"""

import random
import string
import time
import hashlib
import uuid
import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union
from pathlib import Path


def generate_random_string(
    length: int = 8,
    include_letters: bool = True,
    include_digits: bool = True,
    include_symbols: bool = False,
    exclude_ambiguous: bool = True
) -> str:
    """
    生成随机字符串
    
    Args:
        length: 字符串长度
        include_letters: 是否包含字母
        include_digits: 是否包含数字
        include_symbols: 是否包含符号
        exclude_ambiguous: 是否排除易混淆字符
    
    Returns:
        随机字符串
    """
    chars = ""
    
    if include_letters:
        chars += string.ascii_lowercase
    
    if include_digits:
        chars += string.digits
    
    if include_symbols:
        chars += "!@#$%^&*"
    
    if exclude_ambiguous:
        # 排除易混淆的字符
        ambiguous = "0O1lI"
        chars = ''.join(c for c in chars if c not in ambiguous)
    
    if not chars:
        raise ValueError("No character set specified")
    
    return ''.join(random.choices(chars, k=length))


def generate_unique_id() -> str:
    """生成唯一ID"""
    return str(uuid.uuid4()).replace('-', '')


def generate_timestamp_id() -> str:
    """生成基于时间戳的ID"""
    timestamp = str(int(time.time()))
    random_part = generate_random_string(4, include_symbols=False)
    return f"{timestamp}_{random_part}"


def hash_string(text: str, algorithm: str = 'sha256') -> str:
    """
    计算字符串哈希值
    
    Args:
        text: 要哈希的文本
        algorithm: 哈希算法 (md5, sha1, sha256, sha512)
    
    Returns:
        哈希值
    """
    if algorithm not in ['md5', 'sha1', 'sha256', 'sha512']:
        raise ValueError(f"Unsupported hash algorithm: {algorithm}")
    
    hash_obj = hashlib.new(algorithm)
    hash_obj.update(text.encode('utf-8'))
    return hash_obj.hexdigest()


def format_timestamp(timestamp: Optional[float] = None, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化时间戳"""
    if timestamp is None:
        timestamp = time.time()
    
    dt = datetime.fromtimestamp(timestamp)
    return dt.strftime(format_str)


def parse_timestamp(time_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> float:
    """解析时间字符串为时间戳"""
    dt = datetime.strptime(time_str, format_str)
    return dt.timestamp()


def is_expired(timestamp: float, expiry_hours: int = 24) -> bool:
    """检查时间戳是否过期"""
    current_time = time.time()
    expiry_time = timestamp + (expiry_hours * 3600)
    return current_time > expiry_time


def ensure_directory(path: Union[str, Path]) -> Path:
    """确保目录存在"""
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def safe_json_load(file_path: Union[str, Path], default: Any = None) -> Any:
    """安全加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return default


def safe_json_save(data: Any, file_path: Union[str, Path]) -> bool:
    """安全保存JSON文件"""
    try:
        ensure_directory(Path(file_path).parent)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception:
        return False


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """将列表分块"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def flatten_list(nested_list: List[List[Any]]) -> List[Any]:
    """展平嵌套列表"""
    return [item for sublist in nested_list for item in sublist]


def remove_duplicates(lst: List[Any], key_func: Optional[callable] = None) -> List[Any]:
    """移除列表中的重复项"""
    if key_func is None:
        return list(dict.fromkeys(lst))
    
    seen = set()
    result = []
    for item in lst:
        key = key_func(item)
        if key not in seen:
            seen.add(key)
            result.append(item)
    return result


def retry_on_exception(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        sleep_time = delay * (backoff_factor ** attempt)
                        time.sleep(sleep_time)
                    else:
                        break
            
            raise last_exception
        return wrapper
    return decorator


async def async_retry_on_exception(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """异步重试装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        sleep_time = delay * (backoff_factor ** attempt)
                        await asyncio.sleep(sleep_time)
                    else:
                        break
            
            raise last_exception
        return wrapper
    return decorator


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def validate_email_format(email: str) -> bool:
    """简单的邮箱格式验证"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def generate_email_variations(base_email: str, count: int = 5) -> List[str]:
    """生成邮箱变体"""
    if '@' not in base_email:
        raise ValueError("Invalid email format")
    
    prefix, domain = base_email.split('@', 1)
    variations = []
    
    for i in range(count):
        # 添加数字后缀
        new_prefix = f"{prefix}{random.randint(100, 999)}"
        variations.append(f"{new_prefix}@{domain}")
    
    return variations
