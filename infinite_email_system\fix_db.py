#!/usr/bin/env python3
"""
修复数据库连接问题的脚本
"""

import re

def fix_database_connections():
    """修复数据库连接问题"""
    file_path = 'config/database.py'
    
    # 读取文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换所有的 await self.get_connection()
    pattern = r'async with await self\.get_connection\(\) as db:'
    replacement = '''async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row'''
    
    content = re.sub(pattern, replacement, content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 数据库连接问题已修复")

if __name__ == "__main__":
    fix_database_connections()
