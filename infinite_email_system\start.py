#!/usr/bin/env python3
"""
无限邮箱系统启动脚本
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    env_file = project_root / ".env"
    if not env_file.exists():
        print("❌ .env 文件不存在")
        return False
    
    # 检查必要的环境变量
    from dotenv import load_dotenv
    load_dotenv(env_file)
    
    required_vars = [
        'CLOUDFLARE_API_TOKEN',
        'CLOUDFLARE_ZONE_ID', 
        'DOMAIN_NAME',
        'DEFAULT_FORWARD_EMAIL'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value or value == f"请替换为新的{var}":
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少或未配置的环境变量: {', '.join(missing_vars)}")
        print("\n请在 .env 文件中配置以下变量:")
        for var in missing_vars:
            print(f"  {var}=你的值")
        return False
    
    print("✅ 环境配置检查通过")
    return True

def check_dependencies():
    """检查依赖包"""
    print("📦 检查依赖包...")
    
    required_packages = [
        'aiohttp',
        'aiosqlite', 
        'python-dotenv',
        'tkinter'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 依赖包检查通过")
    return True

async def test_cloudflare_connection():
    """测试 Cloudflare 连接"""
    print("🌐 测试 Cloudflare 连接...")
    
    try:
        from core.cloudflare_api import CloudflareEmailAPI
        
        api_token = os.getenv('CLOUDFLARE_API_TOKEN')
        async with CloudflareEmailAPI(api_token) as api:
            if await api.test_connection():
                print("✅ Cloudflare API 连接成功")
                return True
            else:
                print("❌ Cloudflare API 连接失败")
                return False
                
    except Exception as e:
        print(f"❌ Cloudflare API 连接错误: {e}")
        return False

def show_system_info():
    """显示系统信息"""
    domain = os.getenv('DOMAIN_NAME', 'cfish22.dpdns.org')
    forward_email = os.getenv('DEFAULT_FORWARD_EMAIL', '<EMAIL>')
    
    print("\n" + "=" * 60)
    print("🎉 无限邮箱系统已就绪！")
    print("=" * 60)
    print(f"📧 主域名: {domain}")
    print(f"📮 转发邮箱: {forward_email}")
    print(f"🔄 支持: 任意前缀@{domain}")
    print("=" * 60)
    print("\n使用说明:")
    print("1. 系统将启动图形界面")
    print("2. 点击'生成随机邮箱'创建新邮箱地址")
    print("3. 所有邮件将自动转发到您的主邮箱")
    print("4. 可以查看统计信息了解使用情况")
    print("\n正在启动系统...")

async def main():
    """主函数"""
    print("🚀 启动无限邮箱系统...")
    print("=" * 60)
    
    # 1. 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请修复配置后重试")
        return False
    
    # 2. 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装缺少的包后重试")
        return False
    
    # 3. 测试 Cloudflare 连接
    if not await test_cloudflare_connection():
        print("\n⚠️  Cloudflare 连接测试失败，但系统仍可启动")
        print("请检查 API Token 配置是否正确")
    
    # 4. 显示系统信息
    show_system_info()
    
    # 5. 启动主程序
    try:
        from main import main as main_app
        main_app()
        return True
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，系统退出")
        return True
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return False

def quick_setup():
    """快速设置向导"""
    print("🛠️  快速设置向导")
    print("=" * 40)
    
    env_file = project_root / ".env"
    
    # 读取现有配置
    config = {}
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    config[key] = value
    
    # 设置默认值
    config.setdefault('DOMAIN_NAME', 'cfish22.dpdns.org')
    config.setdefault('CLOUDFLARE_ZONE_ID', '733e04386ef1d5d5ffee19a6f74e8b07')
    config.setdefault('DEFAULT_FORWARD_EMAIL', '<EMAIL>')
    
    print(f"域名: {config.get('DOMAIN_NAME')}")
    print(f"转发邮箱: {config.get('DEFAULT_FORWARD_EMAIL')}")
    
    # 检查 API Token
    api_token = config.get('CLOUDFLARE_API_TOKEN', '')
    if not api_token or api_token.startswith('请替换'):
        print("\n⚠️  需要配置 Cloudflare API Token")
        print("请访问 Cloudflare 控制台获取 API Token")
        print("然后在 .env 文件中设置 CLOUDFLARE_API_TOKEN")
        return False
    
    print("✅ 配置检查完成")
    return True

if __name__ == "__main__":
    try:
        # 检查是否需要快速设置
        if len(sys.argv) > 1 and sys.argv[1] == '--setup':
            if quick_setup():
                print("✅ 设置完成，现在可以运行系统")
            else:
                print("❌ 设置未完成")
        else:
            # 正常启动
            asyncio.run(main())
            
    except Exception as e:
        print(f"❌ 启动错误: {e}")
        sys.exit(1)
