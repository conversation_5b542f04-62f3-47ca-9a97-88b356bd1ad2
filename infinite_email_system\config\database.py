"""
数据库操作类
"""

import aiosqlite
import sqlite3
import asyncio
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import json

from utils.helpers import ensure_directory, format_timestamp
from utils.exceptions import DatabaseError
from utils.logger import LoggerMixin
from config.constants import DATA_DIR, DB_NAME


class DatabaseManager(LoggerMixin):
    """数据库管理器"""
    
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = Path(db_path) if db_path else Path(DATA_DIR) / DB_NAME
        self._connection = None
        self._initialized = False
        
        # 确保数据库目录存在
        ensure_directory(self.db_path.parent)
    
    async def initialize(self) -> None:
        """初始化数据库"""
        try:
            await self._create_tables()
            self._initialized = True
            self.logger.info(f"Database initialized: {self.db_path}")
        except Exception as e:
            raise DatabaseError(f"Failed to initialize database: {e}")
    
    async def _create_tables(self) -> None:
        """创建数据库表"""
        async with aiosqlite.connect(self.db_path) as db:
            # 域名表
            await db.execute("""
                CREATE TABLE IF NOT EXISTS domains (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    domain_name TEXT UNIQUE NOT NULL,
                    tld TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'active',
                    freenom_account TEXT,
                    cloudflare_zone_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    last_checked TIMESTAMP,
                    notes TEXT
                )
            """)
            
            # 邮箱表
            await db.execute("""
                CREATE TABLE IF NOT EXISTS emails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email_address TEXT UNIQUE NOT NULL,
                    domain_id INTEGER NOT NULL,
                    prefix TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'active',
                    forward_to TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_used TIMESTAMP,
                    usage_count INTEGER DEFAULT 0,
                    notes TEXT,
                    FOREIGN KEY (domain_id) REFERENCES domains (id) ON DELETE CASCADE
                )
            """)
            
            # 配置表
            await db.execute("""
                CREATE TABLE IF NOT EXISTS configs (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    encrypted BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 日志表
            await db.execute("""
                CREATE TABLE IF NOT EXISTS activity_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    action TEXT NOT NULL,
                    target_type TEXT NOT NULL,
                    target_id TEXT,
                    details TEXT,
                    status TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 统计表
            await db.execute("""
                CREATE TABLE IF NOT EXISTS statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT NOT NULL,
                    metric_value TEXT NOT NULL,
                    date TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(metric_name, date)
                )
            """)
            
            # 创建索引
            await db.execute("CREATE INDEX IF NOT EXISTS idx_emails_domain_id ON emails(domain_id)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_emails_status ON emails(status)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_domains_status ON domains(status)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_activity_logs_action ON activity_logs(action)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_statistics_date ON statistics(date)")
            
            await db.commit()
    
    def get_connection(self):
        """获取数据库连接"""
        if not self._initialized:
            raise DatabaseError("Database not initialized")

        return aiosqlite.connect(self.db_path)
    
    # 域名相关操作
    async def add_domain(self, domain_data: Dict[str, Any]) -> int:
        """添加域名"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                cursor = await db.execute("""
                    INSERT INTO domains (domain_name, tld, status, freenom_account, 
                                       cloudflare_zone_id, expires_at, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    domain_data['domain_name'],
                    domain_data['tld'],
                    domain_data.get('status', 'active'),
                    domain_data.get('freenom_account'),
                    domain_data.get('cloudflare_zone_id'),
                    domain_data.get('expires_at'),
                    domain_data.get('notes')
                ))
                await db.commit()
                
                domain_id = cursor.lastrowid
                await self._log_activity('domain_added', 'domain', str(domain_id), 
                                        f"Added domain: {domain_data['domain_name']}", 'success')
                
                return domain_id
        except Exception as e:
            await self._log_activity('domain_add_failed', 'domain', None, str(e), 'error')
            raise DatabaseError(f"Failed to add domain: {e}")
    
    async def get_domain(self, domain_id: int) -> Optional[Dict[str, Any]]:
        """获取域名信息"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                async with db.execute("SELECT * FROM domains WHERE id = ?", (domain_id,)) as cursor:
                    row = await cursor.fetchone()
                    if row:
                        return dict(row)
                    return None
        except Exception as e:
            raise DatabaseError(f"Failed to get domain: {e}")
    
    async def get_domain_by_name(self, domain_name: str) -> Optional[Dict[str, Any]]:
        """根据域名获取信息"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                async with db.execute("SELECT * FROM domains WHERE domain_name = ?", (domain_name,)) as cursor:
                    row = await cursor.fetchone()
                    if row:
                        return dict(row)
                    return None
        except Exception as e:
            raise DatabaseError(f"Failed to get domain by name: {e}")
    
    async def list_domains(self, status: Optional[str] = None, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """列出域名"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                if status:
                    query = "SELECT * FROM domains WHERE status = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
                    params = (status, limit, offset)
                else:
                    query = "SELECT * FROM domains ORDER BY created_at DESC LIMIT ? OFFSET ?"
                    params = (limit, offset)
                
                async with db.execute(query, params) as cursor:
                    rows = await cursor.fetchall()
                    return [dict(row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to list domains: {e}")
    
    async def update_domain(self, domain_id: int, updates: Dict[str, Any]) -> bool:
        """更新域名信息"""
        try:
            if not updates:
                return True
            
            set_clause = ", ".join([f"{key} = ?" for key in updates.keys()])
            values = list(updates.values()) + [domain_id]
            
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                await db.execute(f"UPDATE domains SET {set_clause} WHERE id = ?", values)
                await db.commit()
                
                await self._log_activity('domain_updated', 'domain', str(domain_id), 
                                        f"Updated domain fields: {list(updates.keys())}", 'success')
                return True
        except Exception as e:
            await self._log_activity('domain_update_failed', 'domain', str(domain_id), str(e), 'error')
            raise DatabaseError(f"Failed to update domain: {e}")
    
    async def delete_domain(self, domain_id: int) -> bool:
        """删除域名"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                # 先删除相关邮箱
                await db.execute("DELETE FROM emails WHERE domain_id = ?", (domain_id,))
                # 删除域名
                await db.execute("DELETE FROM domains WHERE id = ?", (domain_id,))
                await db.commit()
                
                await self._log_activity('domain_deleted', 'domain', str(domain_id), 
                                        f"Deleted domain and related emails", 'success')
                return True
        except Exception as e:
            await self._log_activity('domain_delete_failed', 'domain', str(domain_id), str(e), 'error')
            raise DatabaseError(f"Failed to delete domain: {e}")

    async def get_domain_id_by_name(self, domain_name: str) -> Optional[int]:
        """根据域名获取域名ID"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                async with db.execute("SELECT id FROM domains WHERE domain_name = ?", (domain_name,)) as cursor:
                    row = await cursor.fetchone()
                    return row['id'] if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get domain ID by name: {e}")

    async def get_email_by_address(self, email_address: str) -> Optional[Dict[str, Any]]:
        """根据邮箱地址获取邮箱信息"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                async with db.execute("""
                    SELECT e.*, d.domain_name, d.tld
                    FROM emails e
                    JOIN domains d ON e.domain_id = d.id
                    WHERE e.email_address = ?
                """, (email_address,)) as cursor:
                    row = await cursor.fetchone()
                    return dict(row) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to get email by address: {e}")

    # 邮箱相关操作
    async def save_email(self, email_address: str, domain: str, prefix: str) -> int:
        """保存邮箱地址（简化版本）"""
        try:
            # 获取或创建域名记录
            domain_id = await self.get_domain_id_by_name(domain)
            if not domain_id:
                # 创建域名记录
                # 从域名中提取TLD
                tld = domain.split('.')[-1] if '.' in domain else 'org'
                domain_data = {
                    'domain_name': domain,
                    'tld': tld,
                    'status': 'active',
                    'freenom_account': None,
                    'cloudflare_zone_id': None,
                    'expires_at': None,
                    'notes': 'Auto-created by email system'
                }
                domain_id = await self.add_domain(domain_data)

            # 检查邮箱是否已存在
            existing = await self.get_email_by_address(email_address)
            if existing:
                return existing['id']

            # 添加邮箱记录
            email_data = {
                'email_address': email_address,
                'domain_id': domain_id,
                'prefix': prefix,
                'status': 'active',
                'forward_to': os.getenv('DEFAULT_FORWARD_EMAIL'),
                'notes': f'Generated on {datetime.now().isoformat()}'
            }
            return await self.add_email(email_data)

        except Exception as e:
            self.logger.error(f"Failed to save email {email_address}: {e}")
            raise DatabaseError(f"Failed to save email: {e}")

    async def add_email(self, email_data: Dict[str, Any]) -> int:
        """添加邮箱"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                cursor = await db.execute("""
                    INSERT INTO emails (email_address, domain_id, prefix, status, 
                                      forward_to, notes)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    email_data['email_address'],
                    email_data['domain_id'],
                    email_data['prefix'],
                    email_data.get('status', 'active'),
                    email_data.get('forward_to'),
                    email_data.get('notes')
                ))
                await db.commit()
                
                email_id = cursor.lastrowid
                await self._log_activity('email_added', 'email', str(email_id), 
                                        f"Added email: {email_data['email_address']}", 'success')
                
                return email_id
        except Exception as e:
            await self._log_activity('email_add_failed', 'email', None, str(e), 'error')
            raise DatabaseError(f"Failed to add email: {e}")
    
    async def get_email(self, email_id: int) -> Optional[Dict[str, Any]]:
        """获取邮箱信息"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                async with db.execute("""
                    SELECT e.*, d.domain_name, d.tld 
                    FROM emails e 
                    JOIN domains d ON e.domain_id = d.id 
                    WHERE e.id = ?
                """, (email_id,)) as cursor:
                    row = await cursor.fetchone()
                    if row:
                        return dict(row)
                    return None
        except Exception as e:
            raise DatabaseError(f"Failed to get email: {e}")
    
    async def list_emails(self, domain_id: Optional[int] = None, status: Optional[str] = None, 
                         limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """列出邮箱"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                query = """
                    SELECT e.*, d.domain_name, d.tld 
                    FROM emails e 
                    JOIN domains d ON e.domain_id = d.id 
                    WHERE 1=1
                """
                params = []
                
                if domain_id:
                    query += " AND e.domain_id = ?"
                    params.append(domain_id)
                
                if status:
                    query += " AND e.status = ?"
                    params.append(status)
                
                query += " ORDER BY e.created_at DESC LIMIT ? OFFSET ?"
                params.extend([limit, offset])
                
                async with db.execute(query, params) as cursor:
                    rows = await cursor.fetchall()
                    return [dict(row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to list emails: {e}")
    
    async def update_email_usage(self, email_id: int) -> bool:
        """更新邮箱使用记录"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                await db.execute("""
                    UPDATE emails 
                    SET usage_count = usage_count + 1, last_used = CURRENT_TIMESTAMP 
                    WHERE id = ?
                """, (email_id,))
                await db.commit()
                return True
        except Exception as e:
            raise DatabaseError(f"Failed to update email usage: {e}")
    
    # 活动日志
    async def _log_activity(self, action: str, target_type: str, target_id: Optional[str], 
                           details: str, status: str) -> None:
        """记录活动日志"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                await db.execute("""
                    INSERT INTO activity_logs (action, target_type, target_id, details, status)
                    VALUES (?, ?, ?, ?, ?)
                """, (action, target_type, target_id, details, status))
                await db.commit()
        except Exception:
            # 日志记录失败不应该影响主要操作
            pass
    
    async def get_activity_logs(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取活动日志"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                async with db.execute("""
                    SELECT * FROM activity_logs 
                    ORDER BY created_at DESC 
                    LIMIT ? OFFSET ?
                """, (limit, offset)) as cursor:
                    rows = await cursor.fetchall()
                    return [dict(row) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to get activity logs: {e}")
    
    # 统计信息
    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            async with self.get_connection() as db:
                db.row_factory = aiosqlite.Row
                stats = {}

                # 域名统计
                async with db.execute("SELECT COUNT(*) as count FROM domains WHERE status = 'active'") as cursor:
                    row = await cursor.fetchone()
                    stats['active_domains'] = row['count'] if row else 0

                # 邮箱统计
                async with db.execute("SELECT COUNT(*) as count FROM emails WHERE status = 'active'") as cursor:
                    row = await cursor.fetchone()
                    stats['active_emails'] = row['count'] if row else 0

                # 今日创建的邮箱数
                async with db.execute("""
                    SELECT COUNT(*) as count FROM emails
                    WHERE DATE(created_at) = DATE('now')
                """) as cursor:
                    row = await cursor.fetchone()
                    stats['emails_created_today'] = row['count'] if row else 0

                # 总使用次数
                async with db.execute("SELECT SUM(usage_count) as total FROM emails") as cursor:
                    row = await cursor.fetchone()
                    stats['total_usage'] = row['total'] if row and row['total'] else 0

                return stats
        except Exception as e:
            raise DatabaseError(f"Failed to get statistics: {e}")
    
    async def close(self) -> None:
        """关闭数据库连接"""
        # 由于使用连接池方式，每次都是新连接，这里不需要特别处理
        pass
