"""
Cloudflare API封装
"""

import aiohttp
import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime

from utils.exceptions import CloudflareAPIError
from utils.logger import LoggerMixin
from utils.validators import ConfigValidator
from config.constants import CLOUDFLARE_API_BASE, API_TIMEOUT


class CloudflareEmailAPI(LoggerMixin):
    """Cloudflare邮件路由API完整实现"""
    
    def __init__(self, api_token: str):
        if not ConfigValidator.validate_api_token(api_token):
            raise CloudflareAPIError("Invalid Cloudflare API token format")
        
        self.api_token = api_token
        self.base_url = CLOUDFLARE_API_BASE
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
            "User-Agent": "Infinite-Email-System/1.0"
        }
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=API_TIMEOUT),
            headers=self.headers
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发起API请求"""
        try:
            if not self.session:
                raise CloudflareAPIError("Session not initialized. Use async context manager.")
            
            url = f"{self.base_url}/{endpoint.lstrip('/')}"
            
            self.logger.debug(f"Making {method} request to {url}")
            
            async with self.session.request(method, url, json=data) as response:
                response_data = await response.json()
                
                if response.status >= 400:
                    error_msg = response_data.get('errors', [{'message': 'Unknown error'}])[0]['message']
                    raise CloudflareAPIError(f"API request failed: {error_msg}")
                
                if not response_data.get('success', False):
                    errors = response_data.get('errors', [])
                    error_msg = errors[0]['message'] if errors else 'Unknown error'
                    raise CloudflareAPIError(f"API operation failed: {error_msg}")
                
                return response_data
                
        except aiohttp.ClientError as e:
            raise CloudflareAPIError(f"Network error: {e}")
        except json.JSONDecodeError as e:
            raise CloudflareAPIError(f"Invalid JSON response: {e}")
        except Exception as e:
            if isinstance(e, CloudflareAPIError):
                raise
            raise CloudflareAPIError(f"Unexpected error: {e}")
    
    async def list_zones(self, name: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出所有区域"""
        try:
            endpoint = "zones"
            if name:
                endpoint += f"?name={name}"
            
            response = await self._make_request("GET", endpoint)
            return response.get('result', [])
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to list zones: {e}")
    
    async def add_zone(self, domain: str) -> Dict[str, Any]:
        """添加域名到Cloudflare"""
        try:
            data = {
                "name": domain,
                "type": "full"
            }
            
            response = await self._make_request("POST", "zones", data)
            result = response.get('result', {})
            
            self.logger.info(f"Successfully added zone: {domain}")
            return result
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to add zone {domain}: {e}")
    
    async def get_zone_id(self, domain: str) -> Optional[str]:
        """获取域名的Zone ID"""
        try:
            zones = await self.list_zones(domain)
            
            for zone in zones:
                if zone.get('name') == domain:
                    return zone.get('id')
            
            return None
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to get zone ID for {domain}: {e}")
    
    async def enable_email_routing(self, zone_id: str) -> Dict[str, Any]:
        """启用邮件路由"""
        try:
            response = await self._make_request("POST", f"zones/{zone_id}/email/routing/enable")
            result = response.get('result', {})
            
            self.logger.info(f"Email routing enabled for zone: {zone_id}")
            return result
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to enable email routing: {e}")
    
    async def get_email_routing_settings(self, zone_id: str) -> Dict[str, Any]:
        """获取邮件路由设置"""
        try:
            response = await self._make_request("GET", f"zones/{zone_id}/email/routing")
            return response.get('result', {})
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to get email routing settings: {e}")
    
    async def create_destination_address(self, zone_id: str, email: str) -> Dict[str, Any]:
        """创建目标邮箱地址"""
        try:
            data = {"email": email}
            
            response = await self._make_request("POST", f"zones/{zone_id}/email/routing/addresses", data)
            result = response.get('result', {})
            
            self.logger.info(f"Created destination address: {email}")
            return result
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to create destination address {email}: {e}")
    
    async def list_destination_addresses(self, zone_id: str) -> List[Dict[str, Any]]:
        """列出目标邮箱地址"""
        try:
            response = await self._make_request("GET", f"zones/{zone_id}/email/routing/addresses")
            return response.get('result', [])
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to list destination addresses: {e}")
    
    async def verify_destination_address(self, zone_id: str, address_id: str) -> Dict[str, Any]:
        """验证目标邮箱地址"""
        try:
            response = await self._make_request("POST", f"zones/{zone_id}/email/routing/addresses/{address_id}/verify")
            result = response.get('result', {})
            
            self.logger.info(f"Verification sent for address ID: {address_id}")
            return result
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to verify destination address: {e}")
    
    async def create_catch_all_rule(self, zone_id: str, destination_email: str) -> Dict[str, Any]:
        """创建通配符转发规则"""
        try:
            data = {
                "matchers": [
                    {
                        "type": "all"
                    }
                ],
                "actions": [
                    {
                        "type": "forward",
                        "value": [destination_email]
                    }
                ],
                "enabled": True,
                "name": "Catch All Rule"
            }
            
            response = await self._make_request("POST", f"zones/{zone_id}/email/routing/rules", data)
            result = response.get('result', {})
            
            self.logger.info(f"Created catch-all rule for: {destination_email}")
            return result
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to create catch-all rule: {e}")
    
    async def create_specific_rule(self, zone_id: str, email_pattern: str, destination_email: str) -> Dict[str, Any]:
        """创建特定邮箱转发规则"""
        try:
            data = {
                "matchers": [
                    {
                        "type": "literal",
                        "field": "to",
                        "value": email_pattern
                    }
                ],
                "actions": [
                    {
                        "type": "forward",
                        "value": [destination_email]
                    }
                ],
                "enabled": True,
                "name": f"Rule for {email_pattern}"
            }
            
            response = await self._make_request("POST", f"zones/{zone_id}/email/routing/rules", data)
            result = response.get('result', {})
            
            self.logger.info(f"Created specific rule: {email_pattern} -> {destination_email}")
            return result
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to create specific rule: {e}")
    
    async def list_email_rules(self, zone_id: str) -> List[Dict[str, Any]]:
        """列出邮件转发规则"""
        try:
            response = await self._make_request("GET", f"zones/{zone_id}/email/routing/rules")
            return response.get('result', [])
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to list email rules: {e}")
    
    async def delete_email_rule(self, zone_id: str, rule_id: str) -> bool:
        """删除邮件转发规则"""
        try:
            await self._make_request("DELETE", f"zones/{zone_id}/email/routing/rules/{rule_id}")
            self.logger.info(f"Deleted email rule: {rule_id}")
            return True
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to delete email rule: {e}")
    
    async def setup_complete_email_forwarding(self, domain: str, destination_email: str) -> Dict[str, Any]:
        """完整设置邮件转发"""
        try:
            self.logger.info(f"Setting up email forwarding for {domain} -> {destination_email}")
            
            # 1. 获取或添加域名
            zone_id = await self.get_zone_id(domain)
            if not zone_id:
                zone_result = await self.add_zone(domain)
                zone_id = zone_result.get('id')
                if not zone_id:
                    raise CloudflareAPIError("Failed to get zone ID after adding domain")
            
            # 2. 启用邮件路由
            try:
                await self.enable_email_routing(zone_id)
            except CloudflareAPIError as e:
                if "already enabled" not in str(e).lower():
                    raise
            
            # 3. 创建目标地址
            try:
                dest_result = await self.create_destination_address(zone_id, destination_email)
                dest_id = dest_result.get('id')
                
                # 发送验证邮件
                if dest_id:
                    await self.verify_destination_address(zone_id, dest_id)
                    
            except CloudflareAPIError as e:
                if "already exists" not in str(e).lower():
                    raise
            
            # 4. 创建通配符规则
            try:
                rule_result = await self.create_catch_all_rule(zone_id, destination_email)
            except CloudflareAPIError as e:
                if "already exists" not in str(e).lower():
                    raise
            
            self.logger.info(f"Email forwarding setup completed for {domain}")
            
            return {
                "success": True,
                "zone_id": zone_id,
                "domain": domain,
                "destination": destination_email,
                "message": f"Email forwarding setup complete for {domain}",
                "setup_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to setup email forwarding: {e}")
            return {
                "success": False,
                "domain": domain,
                "error": str(e)
            }
    
    async def get_email_routing_stats(self, zone_id: str) -> Dict[str, Any]:
        """获取邮件路由统计"""
        try:
            # 获取基本设置
            settings = await self.get_email_routing_settings(zone_id)
            
            # 获取规则列表
            rules = await self.list_email_rules(zone_id)
            
            # 获取目标地址列表
            addresses = await self.list_destination_addresses(zone_id)
            
            return {
                "enabled": settings.get('enabled', False),
                "rules_count": len(rules),
                "addresses_count": len(addresses),
                "rules": rules,
                "addresses": addresses,
                "settings": settings
            }
            
        except Exception as e:
            raise CloudflareAPIError(f"Failed to get email routing stats: {e}")
    
    async def test_connection(self) -> bool:
        """测试API连接"""
        try:
            await self.list_zones()
            self.logger.info("Cloudflare API connection test successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Cloudflare API connection test failed: {e}")
            return False
